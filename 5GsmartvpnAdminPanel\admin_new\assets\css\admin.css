/**
 * 5G Smart VPN Admin Panel - Modern CSS Framework
 * Complete responsive admin panel styling
 */

/* ===== CSS VARIABLES ===== */
:root {
    /* Light Theme Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Gray Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Success Colors */
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    /* Warning Colors */
    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    /* Error Colors */
    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;

    /* Info Colors */
    --info-50: #eff6ff;
    --info-500: #3b82f6;
    --info-600: #2563eb;

    /* Layout Variables */
    --sidebar-width: 280px;
    --header-height: 70px;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Dark Theme - User-Friendly Night Mode */
[data-theme="dark"] {
    /* Dark Background Colors - High Contrast & Readable */
    --gray-50: #0a0a0a;          /* Very dark background */
    --gray-100: #1a1a1a;        /* Dark card background */
    --gray-200: #2a2a2a;        /* Slightly lighter background */
    --gray-300: #3a3a3a;        /* Border color */
    --gray-400: #6a6a6a;        /* Muted text */
    --gray-500: #9a9a9a;        /* Secondary text */
    --gray-600: #b5b5b5;        /* Primary text light */
    --gray-700: #d0d0d0;        /* Primary text */
    --gray-800: #e5e5e5;        /* High contrast text */
    --gray-900: #ffffff;        /* Pure white text */

    /* Vibrant Primary Colors for Dark Mode */
    --primary-50: #1a1a2e;
    --primary-100: #16213e;
    --primary-200: #0f3460;
    --primary-300: #0e4b99;
    --primary-400: #2563eb;
    --primary-500: #3b82f6;
    --primary-600: #60a5fa;
    --primary-700: #93c5fd;
    --primary-800: #bfdbfe;
    --primary-900: #dbeafe;

    /* Vibrant Accent Colors */
    --success-50: #0a2e0a;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    --warning-50: #2e1f0a;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    --error-50: #2e0a0a;
    --error-500: #ef4444;
    --error-600: #dc2626;

    --info-50: #0a1a2e;
    --info-500: #06b6d4;
    --info-600: #0891b2;

    /* Special Dark Mode Backgrounds */
    --dark-bg-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --dark-bg-secondary: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    --dark-card-bg: #1a1a1a;
    --dark-sidebar-bg: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
}

/* ===== RESET & BASE STYLES ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--gray-900);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* ===== ADMIN LAYOUT ===== */
.admin-body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.admin-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* ===== SIDEBAR ===== */
.admin-sidebar {
    width: var(--sidebar-width, 280px); /* Fallback for CSS variables */
    width: 280px; /* IE fallback */
    background: white;
    border-right: 1px solid var(--gray-200, #e5e7eb);
    border-right: 1px solid #e5e7eb; /* IE fallback */
    display: -webkit-box; /* Old Safari */
    display: -ms-flexbox; /* IE 10 */
    display: flex;
    -webkit-box-orient: vertical; /* Old Safari */
    -webkit-box-direction: normal; /* Old Safari */
    -ms-flex-direction: column; /* IE 10 */
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: var(--z-fixed, 1000);
    z-index: 1000; /* IE fallback */
    -webkit-transition: -webkit-transform var(--transition-normal, 0.3s ease);
    transition: -webkit-transform var(--transition-normal, 0.3s ease);
    transition: transform var(--transition-normal, 0.3s ease);
    transition: transform var(--transition-normal, 0.3s ease), -webkit-transform var(--transition-normal, 0.3s ease);
    -webkit-transition: -webkit-transform 0.3s ease; /* Safari fallback */
    transition: transform 0.3s ease; /* IE fallback */
}

/* Dark theme sidebar */
[data-theme="dark"] .admin-sidebar {
    background: var(--dark-sidebar-bg);
    border-right: 1px solid var(--gray-300);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.sidebar-header {
    padding: var(--spacing-lg);
    padding-right: calc(var(--spacing-lg) + 50px);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: transparent;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.logo-icon:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius-sm);
    background: transparent;
    transition: all var(--transition-normal);
}

.logo-image:hover {
    transform: scale(1.1);
}

.logo-text .logo-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.logo-text .logo-subtitle {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin: 0;
}

.sidebar-close {
    display: none;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    font-size: 1.25rem;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    width: 36px;
    height: 36px;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    z-index: 1000;
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--gray-800);
    transform: scale(1.05);
}

.sidebar-close:active {
    transform: scale(0.95);
}

/* ===== SIDEBAR NAVIGATION ===== */
.sidebar-nav {
    -webkit-box-flex: 1; /* Old Safari */
    -ms-flex: 1; /* IE 10 */
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md, 1rem) 0;
    padding: 1rem 0; /* IE fallback */
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-divider {
    padding: var(--spacing-md, 1rem) var(--spacing-lg, 1.5rem) var(--spacing-sm, 0.5rem);
    padding: 1rem 1.5rem 0.5rem; /* IE fallback */
}

.divider-text {
    font-size: var(--font-size-xs, 0.75rem);
    font-size: 0.75rem; /* IE fallback */
    font-weight: 600;
    color: var(--gray-400, #9ca3af);
    color: #9ca3af; /* IE fallback */
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.nav-item {
    margin: 0 var(--spacing-md, 1rem);
    margin: 0 1rem; /* IE fallback */
}

.nav-link {
    display: -webkit-box; /* Old Safari */
    display: -ms-flexbox; /* IE 10 */
    display: flex;
    -webkit-box-align: center; /* Old Safari */
    -ms-flex-align: center; /* IE 10 */
    align-items: center;
    gap: var(--spacing-md, 1rem);
    /* IE doesn't support gap, use margin instead */
    padding: var(--spacing-md, 1rem);
    padding: 1rem; /* IE fallback */
    color: var(--gray-600, #4b5563);
    color: #4b5563; /* IE fallback */
    text-decoration: none;
    border-radius: var(--border-radius-sm, 8px);
    border-radius: 8px; /* IE fallback */
    -webkit-transition: all var(--transition-fast, 0.15s ease);
    transition: all var(--transition-fast, 0.15s ease);
    transition: all 0.15s ease; /* IE fallback */
    position: relative;
}

/* IE gap fallback for nav-link children */
.nav-link > * + * {
    margin-left: 1rem;
}

.nav-link:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.nav-link.active {
    background: var(--primary-50);
    color: var(--primary-700);
    font-weight: 500;
}

.nav-icon {
    font-size: 1.25rem;
    width: 20px;
    text-align: center;
}

.nav-text {
    flex: 1;
    font-size: var(--font-size-sm);
}

.nav-badge {
    background: var(--error-500);
    color: white;
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.nav-arrow {
    font-size: 1rem;
    transition: transform var(--transition-fast);
}

/* Submenu */
.nav-submenu {
    max-height: 0;
    overflow: hidden;
    -webkit-transition: max-height var(--transition-normal, 0.3s ease);
    transition: max-height var(--transition-normal, 0.3s ease);
    transition: max-height 0.3s ease; /* IE fallback */
    margin-top: var(--spacing-sm, 0.5rem);
    margin-top: 0.5rem; /* IE fallback */
}

.nav-submenu.show {
    max-height: 300px;
}

.nav-subitem {
    margin-left: var(--spacing-xl, 2rem);
    margin-left: 2rem; /* IE fallback */
}

.nav-sublink {
    display: -webkit-box; /* Old Safari */
    display: -ms-flexbox; /* IE 10 */
    display: flex;
    -webkit-box-align: center; /* Old Safari */
    -ms-flex-align: center; /* IE 10 */
    align-items: center;
    padding: var(--spacing-sm, 0.5rem) var(--spacing-md, 1rem);
    padding: 0.5rem 1rem; /* IE fallback */
    color: var(--gray-500, #6b7280);
    color: #6b7280; /* IE fallback */
    text-decoration: none;
    border-radius: var(--border-radius-sm, 8px);
    border-radius: 8px; /* IE fallback */
    -webkit-transition: all var(--transition-fast, 0.15s ease);
    transition: all var(--transition-fast, 0.15s ease);
    transition: all 0.15s ease; /* IE fallback */
    position: relative;
}

.nav-sublink:before {
    content: '';
    width: 4px;
    height: 4px;
    background: var(--gray-400);
    border-radius: 50%;
    margin-right: var(--spacing-md);
}

.nav-sublink:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.nav-sublink.active {
    background: var(--primary-50);
    color: var(--primary-700);
    font-weight: 500;
}

.nav-sublink.active:before {
    background: var(--primary-500);
}

.nav-subtext {
    font-size: var(--font-size-sm);
}

/* Logout link styling */
.nav-link.text-danger {
    color: var(--error-600) !important;
}

.nav-link.text-danger:hover {
    background: var(--error-50) !important;
    color: var(--error-700) !important;
}

/* ===== SIDEBAR FOOTER ===== */
.sidebar-footer {
    padding: var(--spacing-md);
    margin-top: auto;
    background: transparent;
}

/* User Profile Card */
.user-profile-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-normal);
}

.user-profile-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .user-profile-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .user-profile-card:hover {
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.user-avatar-section {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.user-avatar-wrapper {
    position: relative;
}

.user-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    transition: all var(--transition-normal);
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.user-status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    background: var(--success-500);
    border: 3px solid white;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
    100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
}

.user-info-section {
    text-align: center;
}

.user-details {
    margin-bottom: var(--spacing-md);
}

.user-name {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-xs);
}

.user-role {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.user-role i {
    color: var(--success-500);
}

.user-actions-row {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-sm);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-xs);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    backdrop-filter: blur(5px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-btn:hover {
    border-color: var(--primary-400);
    color: var(--primary-600);
}

.theme-btn:hover {
    border-color: var(--warning-400);
    color: var(--warning-600);
}

[data-theme="dark"] .action-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--gray-300);
}

[data-theme="dark"] .action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Quick Stats */
.quick-stats {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-sm);
    backdrop-filter: blur(5px);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    font-weight: 500;
}

.stat-item i {
    color: var(--success-500);
}

.stat-divider {
    width: 1px;
    height: 16px;
    background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .quick-stats {
    background: rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .stat-item {
    color: var(--gray-400);
}

/* Sidebar specific dropdown positioning */
.sidebar-footer .dropdown {
    position: relative;
}

.sidebar-footer .dropdown-menu {
    position: absolute;
    bottom: 100%;
    top: auto;
    right: 0;
    left: auto;
    margin-bottom: var(--spacing-sm);
    min-width: 200px;
    background: white;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
    padding: var(--spacing-sm) 0;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all var(--transition-fast);
}

.sidebar-footer .dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.sidebar-footer .dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.sidebar-footer .dropdown-item:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.sidebar-footer .dropdown-item.text-danger {
    color: var(--error-600);
}

.sidebar-footer .dropdown-item.text-danger:hover {
    background: var(--error-50);
    color: var(--error-700);
}

.sidebar-footer .dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-sm) 0;
}

.version-info {
    margin-top: var(--spacing-md);
    text-align: center;
    color: var(--gray-400);
    font-size: var(--font-size-xs);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--gray-50);
}

/* Dark theme main content */
[data-theme="dark"] .main-content {
    background: var(--dark-bg-primary);
}

.content-header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--header-height);
}

/* Dark theme content header */
[data-theme="dark"] .content-header {
    background: var(--dark-card-bg);
    border-bottom: 1px solid var(--gray-300);
    backdrop-filter: blur(10px);
}

.header-left .page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.header-left .page-subtitle {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin: 0;
}

.header-right .header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.content-body {
    flex: 1;
    padding: var(--spacing-xl);
}

/* ===== MOBILE MENU TOGGLE ===== */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: var(--spacing-md);
    left: var(--spacing-md);
    z-index: calc(var(--z-fixed) + 1);
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    padding: 0;
    font-size: 1.1rem;
    color: var(--gray-700);
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    width: 36px;
    height: 36px;
    align-items: center;
    justify-content: center;
}

.mobile-menu-toggle:hover {
    background: var(--gray-50);
    transform: scale(1.05);
}

/* ===== MOBILE OVERLAY ===== */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-fixed) - 1);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.mobile-overlay.active {
    opacity: 1;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    /* Enable touch scrolling */
    * {
        -webkit-overflow-scrolling: touch;
    }

    /* Improve touch targets */
    button, .btn, a, input, select, textarea {
        min-height: 44px;
        min-width: 44px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    /* Ensure action buttons are clickable */
    .action-buttons a,
    .action-buttons button {
        position: relative;
        z-index: 10;
        pointer-events: auto;
        cursor: pointer;
        touch-action: manipulation;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.2);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-sm);
        margin: 2px;
        border-radius: var(--border-radius-sm);
        transition: all var(--transition-fast);
    }

    .action-buttons a:active,
    .action-buttons button:active {
        transform: scale(0.95);
        background-color: rgba(0, 0, 0, 0.1);
    }

    /* Fix table cell pointer events */
    .table td {
        pointer-events: auto;
    }

    /* Ensure switches work on mobile */
    .switch,
    .switch input,
    .status-toggle {
        touch-action: manipulation;
        pointer-events: auto;
        cursor: pointer;
    }

    /* Mobile sidebar - completely hidden by default */
    .admin-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        width: 85vw;
        max-width: 320px;
        transform: translateX(-100%);
        z-index: 9999;
        transition: transform 0.3s ease;
        background: white;
        box-shadow: none;
        overflow-y: auto;
    }

    .admin-sidebar.mobile-open {
        transform: translateX(0);
        box-shadow: 2px 0 30px rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(15px);
    }

    .admin-sidebar.mobile-open .sidebar-header {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        color: white;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .admin-sidebar.mobile-open .logo-text .logo-title {
        color: white;
    }

    .admin-sidebar.mobile-open .logo-text .logo-subtitle {
        color: rgba(255, 255, 255, 0.8);
    }

    /* Main content - full width on mobile */
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        min-height: 100vh;
        padding: 0;
        position: relative;
        z-index: 1;
    }

    .mobile-menu-toggle {
        display: flex;
        position: fixed;
        top: var(--spacing-md);
        left: var(--spacing-md);
        z-index: 10000;
        background: var(--primary-600);
        color: white;
        border: none;
        border-radius: var(--border-radius-sm);
        padding: var(--spacing-sm);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .mobile-menu-toggle:hover {
        background: var(--primary-700);
        transform: scale(1.05);
    }

    .mobile-menu-toggle:active {
        transform: scale(0.95);
    }

    .mobile-overlay {
        display: none;
        z-index: 9998;
    }

    .mobile-overlay.active {
        display: block;
    }

    .sidebar-close {
        display: flex;
        background: rgba(255, 255, 255, 0.15);
        color: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        animation: slideInFromRight 0.3s ease-out;
    }

    .sidebar-close:hover {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        transform: scale(1.1);
    }

    @keyframes slideInFromRight {
        from {
            opacity: 0;
            transform: translateX(20px) scale(0.8);
        }
        to {
            opacity: 1;
            transform: translateX(0) scale(1);
        }
    }

    .content-header {
        padding: var(--spacing-md) var(--spacing-lg);
        padding-left: calc(var(--spacing-lg) + 50px);
        margin-top: 0;
        background: white;
        border-bottom: 1px solid var(--gray-200);
        position: sticky;
        top: 0;
        z-index: 100;
    }

    .content-header h1 {
        margin-left: 0;
        font-size: var(--font-size-xl);
    }

    .content-body {
        padding: var(--spacing-lg);
        margin-bottom: 0;
        background: var(--gray-50);
        min-height: calc(100vh - 80px);
    }

    body.sidebar-open {
        overflow: hidden;
    }

    /* Ensure content is not covered by sidebar when closed */
    body:not(.sidebar-open) .main-content {
        transform: translateX(0);
    }

    /* Hide mobile menu toggle when sidebar is open */
    body.sidebar-open .mobile-menu-toggle {
        opacity: 0;
        pointer-events: none;
    }

    /* Sidebar open state - content stays in place, footer hidden via opacity */

    /* Mobile table improvements */
    .table-responsive {
        border-radius: var(--border-radius-sm);
        box-shadow: var(--shadow-sm);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin: 0 calc(-1 * var(--spacing-lg));
    }

    .table {
        min-width: 600px;
        font-size: var(--font-size-sm);
    }

    .table th,
    .table td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
        white-space: nowrap;
    }

    /* Mobile table actions */
    .table .action-buttons {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        min-width: 120px;
    }

    .table .action-buttons .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
        width: 100%;
        justify-content: center;
    }

    /* Mobile form improvements */
    .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .form-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    /* Form controls mobile optimization */
    .form-control, .form-select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: var(--spacing-md);
        border-radius: var(--border-radius-sm);
    }

    .form-label {
        font-size: var(--font-size-sm);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
    }

    /* Mobile card improvements */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .content-header {
        padding: var(--spacing-md) var(--spacing-lg);
        padding-left: calc(var(--spacing-lg) + 45px);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .content-header h1 {
        font-size: var(--font-size-lg);
        margin-left: 0;
    }

    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .content-body {
        padding: var(--spacing-md);
    }

    .main-content {
        padding-bottom: 0; /* Remove excessive padding */
        min-height: calc(100vh - 50px);
    }

    .btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .hide-mobile {
        display: none;
    }

    /* Mobile touch improvements */
    .action-buttons .btn {
        min-width: 44px;
        min-height: 44px;
        padding: var(--spacing-sm);
    }

    .switch {
        min-width: 60px;
        min-height: 34px;
    }

    /* Mobile table scroll indicator */
    .table-responsive::after {
        content: "← Scroll to see more →";
        position: absolute;
        bottom: 0;
        right: 0;
        background: var(--primary-600);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
        border-radius: var(--border-radius-sm) 0 0 0;
        opacity: 0.8;
    }

    /* Mobile modal improvements */
    .modal-dialog {
        margin: var(--spacing-md);
        max-width: calc(100vw - 2 * var(--spacing-md));
    }

    .modal-content {
        border-radius: var(--border-radius-lg);
        max-height: calc(100vh - 4 * var(--spacing-md));
        overflow-y: auto;
    }

    /* Mobile dropdown improvements */
    .dropdown-menu {
        min-width: 200px;
        max-width: calc(100vw - 2 * var(--spacing-md));
        border-radius: var(--border-radius-md);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .dropdown-item {
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    /* Mobile navigation improvements */
    .nav-link {
        padding: var(--spacing-md);
        min-height: 48px;
        display: flex;
        align-items: center;
    }

    /* Mobile search improvements */
    .search-input {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: var(--spacing-md);
        border-radius: var(--border-radius-md);
    }

    /* Mobile pagination improvements */
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .page-link {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-sm);
    }
}

/* ===== COMPONENTS ===== */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1.5;
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-700);
    border-color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: white;
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-success {
    background: var(--success-600);
    color: white;
    border-color: var(--success-600);
}

.btn-success:hover:not(:disabled) {
    background: var(--success-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: var(--warning-600);
    color: white;
    border-color: var(--warning-600);
}

.btn-warning:hover:not(:disabled) {
    background: var(--warning-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background: var(--error-600);
    color: white;
    border-color: var(--error-600);
}

.btn-danger:hover:not(:disabled) {
    background: var(--error-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-ghost {
    background: transparent;
    color: var(--gray-600);
    border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--gray-100);
    color: var(--gray-900);
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
}

/* Cards */
.dashboard-card, .card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.dashboard-card:hover, .card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Dark theme cards */
[data-theme="dark"] .dashboard-card,
[data-theme="dark"] .card {
    background: var(--dark-card-bg);
    border: 1px solid var(--gray-300);
    backdrop-filter: blur(10px);
}

[data-theme="dark"] .dashboard-card:hover,
[data-theme="dark"] .card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    border-color: var(--primary-600);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0 0 var(--spacing-sm);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-600);
}

.stat-change.negative {
    color: var(--error-600);
}

.stat-change.neutral {
    color: var(--gray-500);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: background var(--transition-fast);
}

.activity-item:hover {
    background: var(--gray-50);
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-100);
    color: var(--primary-600);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-size: var(--font-size-sm);
    color: var(--gray-900);
    margin: 0 0 var(--spacing-xs);
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl) var(--spacing-lg);
    color: var(--gray-500);
}

.empty-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg) auto;
}

.empty-icon i {
    font-size: 20px;
    color: var(--gray-400);
}

.empty-text {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--gray-700);
}

.empty-subtext {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* Server List */
.server-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.server-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.server-item:hover {
    border-color: var(--primary-300);
    background: var(--primary-50);
}

.server-flag img {
    width: 32px;
    height: 24px;
    border-radius: 4px;
    object-fit: cover;
}

.server-info {
    flex: 1;
}

.server-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-xs);
}

.server-type {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin: 0;
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.active {
    background: var(--success-50);
    color: var(--success-600);
}

.status-badge.inactive {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Performance Metrics */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-lg);
}

.metric {
    text-align: center;
}

.metric-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-xs);
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

/* ===== FORMS ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--gray-900);
    background: white;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-control::placeholder {
    color: var(--gray-400);
}

.form-control:disabled {
    background: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

/* ===== TABLES ===== */
.table-container {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.table th {
    background: var(--gray-50);
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
}

.table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-100);
    color: var(--gray-900);
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Responsive Table */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
    .table th,
    .table td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .table th:first-child,
    .table td:first-child {
        position: sticky;
        left: 0;
        background: inherit;
        z-index: 1;
    }
}

/* ===== DROPDOWNS ===== */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    bottom: 100%;
    right: 0;
    min-width: 200px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-sm) 0;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-sm);
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.dropdown-item.text-danger {
    color: var(--error-600);
}

.dropdown-item.text-danger:hover {
    background: var(--error-50);
    color: var(--error-700);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-sm) 0;
}

/* ===== FLASH MESSAGES ===== */
.flash-message {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: var(--z-modal);
    max-width: 400px;
    transition: all var(--transition-normal);
}

.flash-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid;
}

.flash-success .flash-content {
    background: var(--success-50);
    border-left-color: var(--success-500);
    color: var(--success-800);
}

.flash-error .flash-content {
    background: var(--error-50);
    border-left-color: var(--error-500);
    color: var(--error-800);
}

.flash-warning .flash-content {
    background: var(--warning-50);
    border-left-color: var(--warning-500);
    color: var(--warning-800);
}

.flash-info .flash-content {
    background: var(--info-50);
    border-left-color: var(--info-500);
    color: var(--info-800);
}

.flash-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.flash-text {
    flex: 1;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.flash-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background var(--transition-fast);
    flex-shrink: 0;
}

.flash-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-normal);
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-600); }
.text-success { color: var(--success-600); }
.text-warning { color: var(--warning-600); }
.text-danger { color: var(--error-600); }
.text-muted { color: var(--gray-500); }

.bg-primary { background-color: var(--primary-600); }
.bg-success { background-color: var(--success-600); }
.bg-warning { background-color: var(--warning-600); }
.bg-danger { background-color: var(--error-600); }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }

.flex-1 { flex: 1; }
.flex-shrink-0 { flex-shrink: 0; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }
.mr-0 { margin-right: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
.pr-0 { padding-right: 0; }

.border-0 { border: 0; }
.border-radius-0 { border-radius: 0; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
    .hide-mobile { display: none !important; }
    .show-mobile { display: block !important; }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .performance-metrics {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    }
}

@media (min-width: 769px) {
    .hide-desktop { display: none !important; }
    .show-desktop { display: block !important; }
}

/* ===== ENHANCED FORM STYLES ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-900);
    font-size: var(--font-size-sm);
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    background: white;
    color: var(--gray-900);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control.error {
    border-color: var(--error-500);
}

.form-control.success {
    border-color: var(--success-500);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

.form-file {
    position: relative;
    overflow: hidden;
    display: inline-block;
    width: 100%;
}

.form-file input[type=file] {
    position: absolute;
    left: -9999px;
}

.form-file-label {
    display: block;
    padding: 2rem 1.5rem;
    background: white;
    border: 2px dashed var(--gray-200);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
    color: var(--gray-500);
    width: 100%;
}

.form-file-label:hover {
    border-color: var(--primary-500);
    background: var(--primary-50);
}

.form-file-label.has-file {
    border-style: solid;
    border-color: var(--primary-500);
    background: var(--primary-50);
    color: var(--primary-700);
}

/* Input Groups */
.input-group {
    display: flex;
    align-items: stretch;
}

.input-group .form-control {
    border-radius: 0;
    border-right: none;
}

.input-group .form-control:first-child {
    border-top-left-radius: var(--border-radius-sm);
    border-bottom-left-radius: var(--border-radius-sm);
}

.input-group .form-control:last-child {
    border-top-right-radius: var(--border-radius-sm);
    border-bottom-right-radius: var(--border-radius-sm);
    border-right: 2px solid var(--gray-200);
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--gray-100);
    border: 2px solid var(--gray-200);
    border-left: none;
    border-top-right-radius: var(--border-radius-sm);
    border-bottom-right-radius: var(--border-radius-sm);
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-500);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Progress Bars */
.progress {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-bar.success {
    background: linear-gradient(90deg, var(--success-500), var(--success-600));
}

.progress-bar.warning {
    background: linear-gradient(90deg, var(--warning-500), var(--warning-600));
}

.progress-bar.danger {
    background: linear-gradient(90deg, var(--error-500), var(--error-600));
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-xs);
    font-weight: 600;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge.primary {
    background: var(--primary-50);
    color: var(--primary-700);
}

.badge.success {
    background: var(--success-50);
    color: var(--success-700);
}

.badge.warning {
    background: var(--warning-50);
    color: var(--warning-600);
}

.badge.danger {
    background: var(--error-50);
    color: var(--error-600);
}

.badge.secondary {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-dot.active {
    background: var(--success-500);
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

.status-dot.inactive {
    background: var(--error-500);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-dot.pending {
    background: var(--warning-500);
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

/* ===== FORM LAYOUT STYLES ===== */
.form-grid {
    display: grid;
    gap: 2rem;
}

.form-section {
    background: var(--gray-50);
    padding: 1.5rem;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
}

.section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-200);
    margin-top: 2rem;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.form-check-label {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

.form-text {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-top: 0.25rem;
}

/* Alert Styles */
.alert {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-sm);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: var(--font-size-sm);
}

.alert-success {
    background: var(--success-50);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

.alert-danger {
    background: var(--error-50);
    color: var(--error-700);
    border: 1px solid var(--error-200);
}

.alert-warning {
    background: var(--warning-50);
    color: var(--warning-700);
    border: 1px solid var(--warning-200);
}

.alert-info {
    background: var(--info-50);
    color: var(--info-700);
    border: 1px solid var(--info-200);
}

/* File Preview */
.file-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.file-preview-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    background: var(--gray-100);
    border: 2px solid var(--gray-200);
}

.file-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-preview-remove {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--gray-500);
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--gray-900);
}

.empty-state p {
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Form Styles */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .file-preview-item {
        width: 80px;
        height: 80px;
    }
}

/* ===== MESSAGES & CONTACTS STYLES ===== */
.messages-list {
    display: flex;
    flex-direction: column;
}

.message-item {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    transition: background-color var(--transition-fast);
}

.message-item:hover {
    background: var(--gray-50);
}

.message-item.unread {
    background: var(--primary-50);
    border-left: 4px solid var(--primary-500);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.message-sender {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sender-avatar {
    width: 48px;
    height: 48px;
    background: var(--gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    font-size: 1.25rem;
}

.sender-info {
    flex: 1;
}

.sender-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
}

.sender-email {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin: 0;
}

.message-meta {
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
}

.message-date {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.message-status.unread {
    background: var(--primary-50);
    color: var(--primary-700);
}

.message-status.read {
    background: var(--warning-50);
    color: var(--warning-700);
}

.message-status.replied {
    background: var(--success-50);
    color: var(--success-700);
}

.message-content {
    margin-bottom: 1rem;
}

.message-subject {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
}

.message-text {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

.message-reply {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
    border-left: 4px solid var(--success-500);
}

.reply-header {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--success-700);
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reply-text {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    margin: 0;
    line-height: 1.6;
}

.message-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* ===== MODAL STYLES ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    padding: 1rem;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* ===== DARK THEME IMPROVEMENTS ===== */
[data-theme="dark"] .admin-sidebar {
    background: var(--dark-sidebar-bg);
    border-right: 1px solid var(--gray-300);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .sidebar-header {
    border-bottom: 1px solid var(--gray-300);
}

[data-theme="dark"] .logo-text .logo-title {
    color: var(--gray-900);
}

[data-theme="dark"] .logo-text .logo-subtitle {
    color: var(--gray-500);
}

[data-theme="dark"] .nav-link {
    color: var(--gray-600);
}

[data-theme="dark"] .nav-link:hover {
    background: var(--gray-200);
    color: var(--gray-900);
}

[data-theme="dark"] .nav-link.active {
    background: var(--primary-200);
    color: var(--primary-900);
}

[data-theme="dark"] .divider-text {
    color: var(--gray-500);
}

[data-theme="dark"] .nav-sublink {
    color: var(--gray-500);
}

[data-theme="dark"] .nav-sublink:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

[data-theme="dark"] .nav-sublink.active {
    background: var(--primary-200);
    color: var(--primary-900);
}

/* Dark Theme - Main Content */
[data-theme="dark"] .admin-main {
    background: var(--gray-50);
}

[data-theme="dark"] .admin-header {
    background: var(--dark-card-bg);
    border-bottom: 1px solid var(--gray-300);
}

[data-theme="dark"] .admin-content {
    background: var(--gray-50);
}

/* Dark Theme - Cards */
[data-theme="dark"] .card {
    background: var(--dark-card-bg);
    border: 1px solid var(--gray-300);
    color: var(--gray-900);
}

[data-theme="dark"] .card-header {
    background: var(--gray-100);
    border-bottom: 1px solid var(--gray-300);
    color: var(--gray-900);
}

[data-theme="dark"] .card-title {
    color: var(--gray-900);
}

[data-theme="dark"] .card-subtitle {
    color: var(--gray-600);
}

/* Dark Theme - Tables */
[data-theme="dark"] .table {
    background: var(--dark-card-bg);
    color: var(--gray-900);
}

[data-theme="dark"] .table th {
    background: var(--gray-100);
    color: var(--gray-900);
    border-bottom: 2px solid var(--gray-300);
}

[data-theme="dark"] .table td {
    border-bottom: 1px solid var(--gray-300);
    color: var(--gray-800);
}

[data-theme="dark"] .table tbody tr:hover {
    background: var(--gray-100);
}

[data-theme="dark"] .table-striped tbody tr:nth-child(odd) {
    background: rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .table-striped tbody tr:nth-child(even) {
    background: rgba(255, 255, 255, 0.05);
}

/* Dark Theme - Forms */
[data-theme="dark"] .form-control {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    color: var(--gray-900);
}

[data-theme="dark"] .form-control:focus {
    background: var(--gray-100);
    border-color: var(--primary-500);
    color: var(--gray-900);
}

[data-theme="dark"] .form-control::placeholder {
    color: var(--gray-500);
}

[data-theme="dark"] .form-label {
    color: var(--gray-800);
}

[data-theme="dark"] .form-text {
    color: var(--gray-500);
}

/* Dark Theme - Buttons */
[data-theme="dark"] .btn-outline-primary {
    color: var(--primary-600);
    border-color: var(--primary-600);
}

[data-theme="dark"] .btn-outline-primary:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}

[data-theme="dark"] .btn-outline-secondary {
    color: var(--gray-600);
    border-color: var(--gray-400);
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background: var(--gray-600);
    border-color: var(--gray-600);
    color: white;
}

/* Dark Theme - Badges */
[data-theme="dark"] .badge.primary {
    background: var(--primary-200);
    color: var(--primary-900);
}

[data-theme="dark"] .badge.success {
    background: var(--success-50);
    color: var(--success-500);
}

[data-theme="dark"] .badge.warning {
    background: var(--warning-50);
    color: var(--warning-500);
}

[data-theme="dark"] .badge.danger {
    background: var(--error-50);
    color: var(--error-500);
}

[data-theme="dark"] .badge.secondary {
    background: var(--gray-200);
    color: var(--gray-800);
}

/* Dark Theme - Alerts */
[data-theme="dark"] .alert-success {
    background: var(--success-50);
    color: var(--success-500);
    border-color: var(--success-500);
}

[data-theme="dark"] .alert-danger {
    background: var(--error-50);
    color: var(--error-500);
    border-color: var(--error-500);
}

[data-theme="dark"] .alert-warning {
    background: var(--warning-50);
    color: var(--warning-500);
    border-color: var(--warning-500);
}

[data-theme="dark"] .alert-info {
    background: var(--info-50);
    color: var(--info-500);
    border-color: var(--info-500);
}

/* Dark Theme - Modals */
[data-theme="dark"] .modal-content {
    background: var(--dark-card-bg);
    color: var(--gray-900);
}

[data-theme="dark"] .modal-header {
    border-bottom: 1px solid var(--gray-300);
}

[data-theme="dark"] .modal-header h3 {
    color: var(--gray-900);
}

[data-theme="dark"] .modal-footer {
    border-top: 1px solid var(--gray-300);
}

/* Dark Theme - Dropdowns */
[data-theme="dark"] .dropdown-menu {
    background: var(--dark-card-bg);
    border: 1px solid var(--gray-300);
    color: var(--gray-900);
}

[data-theme="dark"] .dropdown-item {
    color: var(--gray-800);
}

[data-theme="dark"] .dropdown-item:hover {
    background: var(--gray-200);
    color: var(--gray-900);
}

/* Dark Theme - Text Colors */
[data-theme="dark"] .text-muted {
    color: var(--gray-500) !important;
}

[data-theme="dark"] .text-primary {
    color: var(--primary-600) !important;
}

[data-theme="dark"] .text-success {
    color: var(--success-500) !important;
}

[data-theme="dark"] .text-warning {
    color: var(--warning-500) !important;
}

[data-theme="dark"] .text-danger {
    color: var(--error-500) !important;
}

/* Dark Theme - Dashboard Components */
[data-theme="dark"] .stat-card {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    border: 1px solid var(--gray-300);
    color: var(--gray-900);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .stat-card:hover {
    background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-100) 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    transform: translateY(-4px);
}

[data-theme="dark"] .stat-icon {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

[data-theme="dark"] .stat-card:hover .stat-icon {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6);
    transform: scale(1.05);
}

[data-theme="dark"] .stat-number {
    color: var(--gray-900);
}

[data-theme="dark"] .stat-label {
    color: var(--gray-600);
}

[data-theme="dark"] .stat-change.positive {
    color: var(--success-500);
}

[data-theme="dark"] .stat-change.negative {
    color: var(--error-500);
}

[data-theme="dark"] .stat-change.neutral {
    color: var(--gray-500);
}

/* Dark Theme - Dashboard Cards */
[data-theme="dark"] .dashboard-card {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    border: 1px solid var(--gray-300);
    color: var(--gray-900);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .dashboard-card:hover {
    background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-100) 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    transform: translateY(-3px);
}

[data-theme="dark"] .dashboard-card .card-header {
    background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-300) 100%);
    border-bottom: 1px solid var(--gray-400);
}

[data-theme="dark"] .dashboard-card .card-title {
    color: var(--gray-900);
}

/* Dark Theme - Activity List */
[data-theme="dark"] .activity-item:hover {
    background: var(--gray-200);
}

[data-theme="dark"] .activity-icon {
    background: var(--primary-200);
    color: var(--primary-900);
}

[data-theme="dark"] .activity-text {
    color: var(--gray-800);
}

[data-theme="dark"] .activity-time {
    color: var(--gray-500);
}

/* Dark Theme - Server List */
[data-theme="dark"] .server-item {
    border: 1px solid var(--gray-300);
    background: var(--gray-100);
}

[data-theme="dark"] .server-item:hover {
    border-color: var(--primary-500);
    background: var(--primary-200);
}

[data-theme="dark"] .server-name {
    color: var(--gray-900);
}

[data-theme="dark"] .server-type {
    color: var(--gray-600);
}

[data-theme="dark"] .status-badge.active {
    background: var(--success-50);
    color: var(--success-500);
}

[data-theme="dark"] .status-badge.inactive {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* Dark Theme - Performance Metrics */
[data-theme="dark"] .metric-value {
    color: var(--gray-900);
}

[data-theme="dark"] .metric-label {
    color: var(--gray-600);
}

/* Dark Theme - Table Container */
[data-theme="dark"] .table-container {
    background: var(--dark-card-bg);
    border: 1px solid var(--gray-300);
}

/* Dark Theme - Loading Screen */
[data-theme="dark"] .loading-screen {
    background: var(--gray-50);
}

[data-theme="dark"] .loading-spinner p {
    color: var(--gray-600);
}

[data-theme="dark"] .spinner {
    border-color: var(--gray-300);
    border-top-color: var(--primary-500);
}

/* ===== RESPONSIVE MODAL ===== */
@media (max-width: 768px) {
    .modal {
        padding: 0;
        align-items: flex-end;
    }

    .modal-content {
        max-height: 95vh;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .message-meta {
        align-items: flex-start;
        text-align: left;
    }

    .message-actions {
        justify-content: flex-start;
    }

    .message-actions .btn {
        flex: 1;
        min-width: 0;
    }
}

/* ===== SETTINGS TABS STYLES ===== */
.settings-tabs {
    display: flex;
    flex-direction: column;
    gap: 0;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.tab-nav {
    display: flex;
    background: var(--gray-100);
    border-bottom: 1px solid var(--gray-200);
}

.tab-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

.tab-btn.active {
    background: white;
    color: var(--primary-600);
    border-bottom-color: var(--primary-500);
}

.tab-content {
    flex: 1;
}

.tab-pane {
    display: none;
    padding: 0;
}

.tab-pane.active {
    display: block;
}

.tab-pane .card {
    border: none;
    box-shadow: none;
    margin: 0;
}

.tab-pane .card:not(:last-child) {
    border-bottom: 1px solid var(--gray-200);
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
}

.form-check:hover {
    background: var(--gray-100);
}

.changed {
    border-color: var(--warning-500) !important;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1) !important;
}

/* ===== RESPONSIVE TABS ===== */
@media (max-width: 768px) {
    .tab-nav {
        flex-direction: column;
    }

    .tab-btn {
        justify-content: flex-start;
        border-bottom: none;
        border-left: 3px solid transparent;
    }

    .tab-btn.active {
        border-left-color: var(--primary-500);
        border-bottom-color: transparent;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== MOBILE UTILITY CLASSES ===== */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }

    .mobile-visible {
        display: block !important;
    }

    .mobile-flex {
        display: flex !important;
    }

    .mobile-full-width {
        width: 100% !important;
    }

    .mobile-text-center {
        text-align: center !important;
    }

    .mobile-text-left {
        text-align: left !important;
    }

    .mobile-no-padding {
        padding: 0 !important;
    }

    .mobile-small-padding {
        padding: var(--spacing-sm) !important;
    }

    .mobile-stack {
        flex-direction: column !important;
    }

    .mobile-wrap {
        flex-wrap: wrap !important;
    }

    /* Mobile-specific button improvements */
    .btn-mobile-block {
        display: block !important;
        width: 100% !important;
        margin-bottom: var(--spacing-sm) !important;
    }

    /* Mobile-specific spacing */
    .mobile-mt-1 { margin-top: var(--spacing-xs) !important; }
    .mobile-mt-2 { margin-top: var(--spacing-sm) !important; }
    .mobile-mt-3 { margin-top: var(--spacing-md) !important; }
    .mobile-mb-1 { margin-bottom: var(--spacing-xs) !important; }
    .mobile-mb-2 { margin-bottom: var(--spacing-sm) !important; }
    .mobile-mb-3 { margin-bottom: var(--spacing-md) !important; }

    /* Mobile-specific text sizes */
    .mobile-text-xs { font-size: var(--font-size-xs) !important; }
    .mobile-text-sm { font-size: var(--font-size-sm) !important; }
    .mobile-text-base { font-size: var(--font-size-base) !important; }
}

/* ===== FLAG SELECTION STYLING ===== */
.flag-selection-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.flag-selection-container .form-control {
    flex: 1;
    min-width: 200px;
}

.flag-preview {
    display: flex;
    align-items: center;
}

.flag-preview img {
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.flag-preview img:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

[data-theme="dark"] .flag-preview img {
    border-color: var(--gray-400);
}

/* Flag selection dropdown enhancements */
.flag-selection-container .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

[data-theme="dark"] .flag-selection-container .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Responsive flag selection */
@media (max-width: 640px) {
    .flag-selection-container {
        flex-direction: column;
        align-items: stretch;
    }

    .flag-selection-container .form-control {
        min-width: auto;
    }

    .flag-preview {
        justify-content: center;
        margin-top: var(--spacing-sm);
    }
}

/* ===== BROWSER COMPATIBILITY FIXES ===== */

/* Internet Explorer 11 and Edge Legacy fixes */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    /* IE11 specific fixes */
    .admin-sidebar {
        width: 280px !important;
        background: white !important;
        border-right: 1px solid #e5e7eb !important;
    }

    .nav-link {
        display: -ms-flexbox !important;
        -ms-flex-align: center !important;
    }

    .nav-submenu {
        transition: max-height 0.3s ease !important;
    }

    /* Force visibility for nav items */
    .nav-item,
    .nav-subitem {
        display: block !important;
        visibility: visible !important;
    }

    .nav-link,
    .nav-sublink {
        display: -ms-flexbox !important;
        visibility: visible !important;
    }
}

/* Firefox specific fixes */
@-moz-document url-prefix() {
    .admin-sidebar {
        scrollbar-width: thin;
        scrollbar-color: #cbd5e0 #f7fafc;
    }

    .nav-submenu {
        transition: max-height 0.3s ease;
    }
}

/* Safari specific fixes */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) {
        .admin-sidebar {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }

        .nav-link {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }
    }
}

/* Ensure menu items are always visible regardless of browser */
.nav-item,
.nav-subitem,
.nav-link,
.nav-sublink {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.nav-link {
    display: flex !important;
    display: -webkit-box !important;
    display: -ms-flexbox !important;
}

/* Force submenu visibility when active */
.nav-submenu.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: 500px !important;
}

/* Fallback theme classes for browsers that don't support data attributes */
.theme-light .admin-sidebar {
    background: white;
    border-right: 1px solid #e5e7eb;
}

.theme-dark .admin-sidebar {
    background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
    border-right: 1px solid #374151;
}

/* Cross-browser transform support */
.nav-arrow {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: -webkit-transform 0.15s ease;
    -moz-transition: -moz-transform 0.15s ease;
    -ms-transition: -ms-transform 0.15s ease;
    -o-transition: -o-transform 0.15s ease;
    transition: transform 0.15s ease;
}

/* Ensure Provider Registrations menu item is always visible */
.nav-sublink[href="provider-registrations.php"],
a[href="provider-registrations.php"] {
    display: flex !important;
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}
