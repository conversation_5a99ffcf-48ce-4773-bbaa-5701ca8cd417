/*
 * Copyright (c) 2024 OpenVPN API V2
 * VpnService-based OpenVPN API without native binary execution
 * Compatible with Android 10+ and SELinux restrictions
 */

package de.blinkt.openvpn;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import android.annotation.TargetApi;


import de.blinkt.openvpn.core.ConfigParser;
import de.blinkt.openvpn.core.OpenVPNServiceV2;
import de.blinkt.openvpn.core.ProfileManager;
import de.blinkt.openvpn.core.VpnStatus;

/**
 * VpnService-based OpenVPN API that doesn't rely on native binary execution.
 * This API uses Android's VpnService directly to establish VPN connections,
 * making it compatible with Android 10+ and avoiding SELinux permission issues.
 */
public class OpenVpnApiV2 {
    
    private static final String TAG = "OpenVpnApiV2";
    
    /**
     * Start VPN connection using VpnService-based implementation
     * 
     * @param context Application context
     * @param inlineConfig OpenVPN configuration as string
     * @param sCountry Server country name
     * @param userName Username for authentication (can be null)
     * @param password Password for authentication (can be null)
     * @throws RemoteException if configuration is invalid
     */
    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1)
    public static void startVpn(Context context, String inlineConfig, String sCountry, String userName, String password) throws RemoteException {
        if (TextUtils.isEmpty(inlineConfig)) {
            throw new RemoteException("OpenVPN config is empty");
        }
        
        Log.d(TAG, "Starting VPN with VpnService-based implementation");
        Log.d(TAG, "Country: " + sCountry);
        Log.d(TAG, "Username: " + (userName != null ? userName : "not provided"));
        
        try {
            startVpnInternal(context, inlineConfig, sCountry, userName, password);
        } catch (Exception e) {
            Log.e(TAG, "Error starting VPN", e);
            throw new RemoteException("Failed to start VPN: " + e.getMessage());
        }
    }
    
    /**
     * Internal method to start VPN connection
     */
    private static void startVpnInternal(Context context, String inlineConfig, String sCountry, String userName, String password) {
        try {
            // Create VPN profile for validation
            VpnProfile profile = createVpnProfile(inlineConfig, sCountry, userName, password);

            if (profile == null) {
                Log.e(TAG, "Failed to create VPN profile");
                VpnStatus.logError("Failed to create VPN profile from configuration");
                return;
            }

            // Validate profile
            int profileCheck = profile.checkProfile(context);
            if (profileCheck != R.string.no_error_found) {
                Log.e(TAG, "VPN profile validation failed: " + profileCheck);
                VpnStatus.logError("VPN profile validation failed");
                return;
            }

            // Start VPN service with configuration string
            Intent vpnIntent = new Intent(context, OpenVPNServiceV2.class);
            vpnIntent.setAction("CONNECT");
            vpnIntent.putExtra("config", inlineConfig);
            vpnIntent.putExtra("username", userName);
            vpnIntent.putExtra("password", password);
            vpnIntent.putExtra("profile", profile); // Keep profile for additional validation

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(vpnIntent);
            } else {
                context.startService(vpnIntent);
            }

            Log.d(TAG, "VPN service started successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error starting VPN service", e);
            VpnStatus.logError("Error starting VPN: " + e.getMessage());
        }
    }
    
    /**
     * Stop VPN connection
     */
    public static void stopVpn(Context context) {
        Log.d(TAG, "Stopping VPN connection - initiating service termination");

        try {
            // CRITICAL FIX: Send disconnect action to service
            Intent vpnIntent = new Intent(context, OpenVPNServiceV2.class);
            vpnIntent.setAction("DISCONNECT");
            context.startService(vpnIntent);

            // CRITICAL FIX: Add fallback mechanism to ensure service stops
            // Schedule a delayed service stop in case the service doesn't stop itself
            new android.os.Handler().postDelayed(() -> {
                try {
                    Log.d(TAG, "Executing fallback service stop");
                    Intent stopIntent = new Intent(context, OpenVPNServiceV2.class);
                    context.stopService(stopIntent);
                    Log.d(TAG, "Fallback service stop completed");
                } catch (Exception e) {
                    Log.w(TAG, "Error in fallback service stop", e);
                }
            }, 3000); // Wait 3 seconds before fallback

            Log.d(TAG, "VPN stop request sent successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error stopping VPN service", e);

            // CRITICAL FIX: Force stop service if normal stop fails
            try {
                Log.d(TAG, "Attempting force stop of VPN service");
                Intent stopIntent = new Intent(context, OpenVPNServiceV2.class);
                context.stopService(stopIntent);
            } catch (Exception ex) {
                Log.e(TAG, "Force stop also failed", ex);
            }
        }
    }
    
    /**
     * Create VPN profile from configuration
     */
    private static VpnProfile createVpnProfile(String inlineConfig, String sCountry, String userName, String password) {
        try {
            // Parse the inline configuration
            ConfigParser cp = new ConfigParser();
            cp.parseConfig(new java.io.StringReader(inlineConfig));
            
            // Convert to VPN profile
            VpnProfile profile = cp.convertProfile();
            
            if (profile == null) {
                Log.e(TAG, "Failed to convert config to profile");
                return null;
            }
            
            // Set profile properties
            profile.mName = sCountry != null ? sCountry : "VPN Server";
            profile.mProfileCreator = "OpenVPN API V2";
            
            // Set authentication if provided
            if (!TextUtils.isEmpty(userName) && !TextUtils.isEmpty(password)) {
                profile.mUsername = userName;
                profile.mPassword = password;
                
                // If no auth type is set, assume username/password
                if (profile.mAuthenticationType == VpnProfile.TYPE_CERTIFICATES) {
                    profile.mAuthenticationType = VpnProfile.TYPE_USERPASS_CERTIFICATES;
                } else if (profile.mAuthenticationType == 0) {
                    profile.mAuthenticationType = VpnProfile.TYPE_USERPASS;
                }
            }
            
            // Set some default options for better compatibility
            profile.mUsePull = true;
            profile.mUseDefaultRoute = true;
            profile.mAllowLocalLAN = false;
            profile.mBlockUnusedAddressFamilies = true;
            
            Log.d(TAG, "Created VPN profile: " + profile.mName);
            Log.d(TAG, "Auth type: " + profile.mAuthenticationType);
            
            return profile;
            
        } catch (Exception e) {
            Log.e(TAG, "Error creating VPN profile", e);
            return null;
        }
    }
    
    /**
     * Check if VPN is currently connected
     */
    public static boolean isVpnConnected(Context context) {
        // This is a simplified check
        // In a real implementation, you might want to bind to the service
        // and check its connection status
        return false;
    }
    
    /**
     * Get current VPN status
     */
    public static String getVpnStatus(Context context) {
        // This would typically query the service for current status
        return "DISCONNECTED";
    }
    
    /**
     * Service connection helper for binding to VPN service
     */
    public static class VpnServiceConnection implements ServiceConnection {
        private OpenVPNServiceV2.LocalBinder serviceBinder;
        private VpnServiceCallback callback;
        
        public interface VpnServiceCallback {
            void onServiceConnected(OpenVPNServiceV2 service);
            void onServiceDisconnected();
        }
        
        public VpnServiceConnection(VpnServiceCallback callback) {
            this.callback = callback;
        }
        
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            serviceBinder = (OpenVPNServiceV2.LocalBinder) service;
            OpenVPNServiceV2 vpnService = serviceBinder.getService();
            
            if (callback != null) {
                callback.onServiceConnected(vpnService);
            }
        }
        
        @Override
        public void onServiceDisconnected(ComponentName name) {
            serviceBinder = null;
            
            if (callback != null) {
                callback.onServiceDisconnected();
            }
        }
        
        public OpenVPNServiceV2 getService() {
            return serviceBinder != null ? serviceBinder.getService() : null;
        }
    }
    
    /**
     * Bind to VPN service
     */
    public static VpnServiceConnection bindToVpnService(Context context, VpnServiceConnection.VpnServiceCallback callback) {
        VpnServiceConnection connection = new VpnServiceConnection(callback);
        
        Intent intent = new Intent(context, OpenVPNServiceV2.class);
        context.bindService(intent, connection, Context.BIND_AUTO_CREATE);
        
        return connection;
    }
    
    /**
     * Unbind from VPN service
     */
    public static void unbindFromVpnService(Context context, VpnServiceConnection connection) {
        if (connection != null) {
            try {
                context.unbindService(connection);
            } catch (Exception e) {
                Log.w(TAG, "Error unbinding from service", e);
            }
        }
    }
}
