<?php
/**
 * Custom Ads Provider Login System
 * Handles WhatsApp-based authentication for providers
 */

session_start();
require_once '../admin_new/includes/config.php';

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $whatsapp = trim($_POST['whatsapp'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($whatsapp) || empty($password)) {
        $error = 'Please enter both WhatsApp number and password.';
    } else {
        // Clean WhatsApp number (remove spaces, dashes, etc.)
        $whatsapp = preg_replace('/[^0-9+]/', '', $whatsapp);
        
        // Check provider credentials
        $stmt = $conn->prepare("SELECT * FROM custom_ads_providers WHERE whatsapp_number = ? AND status IN ('active', 'pending')");
        $stmt->bind_param("s", $whatsapp);
        $stmt->execute();
        $result = $stmt->get_result();
        $provider = $result->fetch_assoc();
        
        if ($provider && password_verify($password, $provider['password_hash'])) {
            if ($provider['status'] === 'suspended') {
                $error = 'Your account has been suspended. Please contact support.';
            } else {
                // Login successful
                $_SESSION['provider_id'] = $provider['provider_id'];
                $_SESSION['provider_name'] = $provider['contact_person'];
                $_SESSION['business_name'] = $provider['business_name'];
                
                // Update last login
                $stmt = $conn->prepare("UPDATE custom_ads_providers SET last_login = NOW() WHERE provider_id = ?");
                $stmt->bind_param("s", $provider['provider_id']);
                $stmt->execute();
                
                header("Location: index.php");
                exit;
            }
        } else {
            $error = 'Invalid WhatsApp number or password.';
        }
    }
}

// Handle registration form submission
if (isset($_POST['register'])) {
    $whatsapp = trim($_POST['reg_whatsapp'] ?? '');
    $business_name = trim($_POST['business_name'] ?? '');
    $contact_person = trim($_POST['contact_person'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['reg_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';


    
    if (empty($whatsapp) || empty($business_name) || empty($contact_person) || empty($password)) {
        $error = 'Please fill in all required fields.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } else {
        // Clean WhatsApp number
        $whatsapp = preg_replace('/[^0-9+]/', '', $whatsapp);
        
        // Check if WhatsApp number already exists
        $stmt = $conn->prepare("SELECT id FROM custom_ads_providers WHERE whatsapp_number = ?");
        $stmt->bind_param("s", $whatsapp);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $error = 'This WhatsApp number is already registered.';
        } else {
            // Generate provider ID
            $provider_id = 'PROV' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Check if provider ID already exists
            $stmt = $conn->prepare("SELECT id FROM custom_ads_providers WHERE provider_id = ?");
            $stmt->bind_param("s", $provider_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($result->num_rows > 0) {
                $provider_id = 'PROV' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $stmt->bind_param("s", $provider_id);
                $stmt->execute();
                $result = $stmt->get_result();
            }
            
            // Hash password
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new provider
            $stmt = $conn->prepare("INSERT INTO custom_ads_providers (provider_id, whatsapp_number, business_name, contact_person, email, password_hash, status) VALUES (?, ?, ?, ?, ?, ?, 'pending')");
            $stmt->bind_param("ssssss", $provider_id, $whatsapp, $business_name, $contact_person, $email, $password_hash);
            
            if ($stmt->execute()) {
                $success = 'Registration successful! Your account is pending approval. You will be notified once approved.';


            } else {
                $error = 'Registration failed. Please try again. Error: ' . $stmt->error;


            }
        }
    }
}

// Check for error messages from URL
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'invalid_provider':
            $error = 'Invalid provider account. Please contact support.';
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Provider Login - 5G Smart VPN Custom Ads</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .nav-pills .nav-link {
            border-radius: 15px;
        }
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card login-card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold">5G Smart VPN</h2>
                            <p class="text-muted">Custom Ads Provider Portal</p>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                <hr>
                                <p class="mb-0">
                                    <small>
                                        <strong>Next Steps:</strong><br>
                                        1. Your account will be reviewed by our team<br>
                                        2. You'll receive a WhatsApp notification once approved<br>
                                        3. You can then login and start creating ads
                                    </small>
                                </p>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Navigation Tabs -->
                        <ul class="nav nav-pills nav-justified mb-4" id="authTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="login-tab" data-bs-toggle="pill" data-bs-target="#login" type="button" role="tab">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="register-tab" data-bs-toggle="pill" data-bs-target="#register" type="button" role="tab">
                                    <i class="fas fa-user-plus me-2"></i>Register
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content" id="authTabsContent">
                            <!-- Login Tab -->
                            <div class="tab-pane fade show active" id="login" role="tabpanel">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="whatsapp" class="form-label">
                                            <i class="fab fa-whatsapp me-2"></i>WhatsApp Number
                                        </label>
                                        <input type="text" class="form-control" id="whatsapp" name="whatsapp" 
                                               placeholder="e.g., +*************" required>
                                        <div class="form-text">Enter your registered WhatsApp number</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="password" class="form-label">
                                            <i class="fas fa-lock me-2"></i>Password
                                        </label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-sign-in-alt me-2"></i>Login to Dashboard
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Register Tab -->
                            <div class="tab-pane fade" id="register" role="tabpanel">
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="reg_whatsapp" class="form-label">
                                                <i class="fab fa-whatsapp me-2"></i>WhatsApp Number *
                                            </label>
                                            <input type="text" class="form-control" id="reg_whatsapp" name="reg_whatsapp"
                                                   placeholder="+*************" required
                                                   pattern="[\+]?[0-9]{10,15}"
                                                   title="Please enter a valid WhatsApp number (10-15 digits)">
                                            <div class="form-text">
                                                <small>Enter your active WhatsApp number (e.g., +*************)</small>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="business_name" class="form-label">
                                                <i class="fas fa-building me-2"></i>Business Name *
                                            </label>
                                            <input type="text" class="form-control" id="business_name" name="business_name" required>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="contact_person" class="form-label">
                                                <i class="fas fa-user me-2"></i>Contact Person *
                                            </label>
                                            <input type="text" class="form-control" id="contact_person" name="contact_person" required>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope me-2"></i>Email
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="reg_password" class="form-label">
                                                <i class="fas fa-lock me-2"></i>Password *
                                            </label>
                                            <input type="password" class="form-control" id="reg_password" name="reg_password" 
                                                   minlength="6" required>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="confirm_password" class="form-label">
                                                <i class="fas fa-lock me-2"></i>Confirm Password *
                                            </label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   minlength="6" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="terms" required>
                                            <label class="form-check-label" for="terms">
                                                I agree to the <a href="#" target="_blank">Terms and Conditions</a>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="register" class="btn btn-primary">
                                            <i class="fas fa-user-plus me-2"></i>Register Account
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <div class="mb-2">
                                <a href="check_registration.php" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-search me-2"></i>Check Registration Status
                                </a>
                            </div>
                            <small class="text-muted">
                                Need help? Contact us on WhatsApp:
                                <a href="https://wa.me/*************" target="_blank">+880 1712-345678</a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add form submission debugging
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.querySelector('#register form');
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    console.log('Registration form submitted');

                    // Get form data
                    const formData = new FormData(this);
                    console.log('Form data:');
                    for (let [key, value] of formData.entries()) {
                        console.log(key + ': ' + value);
                    }

                    // Check required fields
                    const requiredFields = ['reg_whatsapp', 'business_name', 'contact_person', 'reg_password', 'confirm_password'];
                    let missingFields = [];

                    requiredFields.forEach(field => {
                        const input = this.querySelector(`[name="${field}"]`);
                        if (!input || !input.value.trim()) {
                            missingFields.push(field);
                        }
                    });

                    if (missingFields.length > 0) {
                        console.error('Missing required fields:', missingFields);
                        alert('Please fill in all required fields: ' + missingFields.join(', '));
                        e.preventDefault();
                        return false;
                    }

                    // Check password match
                    const password = this.querySelector('[name="reg_password"]').value;
                    const confirmPassword = this.querySelector('[name="confirm_password"]').value;

                    if (password !== confirmPassword) {
                        console.error('Passwords do not match');
                        alert('Passwords do not match');
                        e.preventDefault();
                        return false;
                    }

                    // Check terms checkbox
                    const termsCheckbox = this.querySelector('#terms');
                    if (!termsCheckbox.checked) {
                        console.error('Terms not accepted');
                        alert('Please accept the Terms and Conditions');
                        e.preventDefault();
                        return false;
                    }

                    console.log('Form validation passed, submitting...');
                });
            }
        });
    </script>
</body>
</html>
