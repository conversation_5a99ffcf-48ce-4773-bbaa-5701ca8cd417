<?php
/**
 * Registration Status Check Page
 * Allows users to check their registration status
 */

session_start();
require_once __DIR__ . '/../admin_new/includes/config.php';

$message = '';
$provider = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $whatsapp = trim($_POST['whatsapp'] ?? '');
    
    if (empty($whatsapp)) {
        $message = 'Please enter your WhatsApp number.';
    } else {
        // Clean WhatsApp number
        $whatsapp = preg_replace('/[^0-9+]/', '', $whatsapp);
        
        // Check provider status
        $stmt = $conn->prepare("SELECT provider_id, whatsapp_number, business_name, contact_person, email, status, registration_date FROM custom_ads_providers WHERE whatsapp_number = ?");
        $stmt->bind_param("s", $whatsapp);
        $stmt->execute();
        $result = $stmt->get_result();
        $provider = $result->fetch_assoc();
        
        if (!$provider) {
            $message = 'No registration found for this WhatsApp number. Please check the number or register a new account.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Registration Status - 5G Smart VPN Custom Ads</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .status-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 8px 16px;
        }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-active { background-color: #198754; color: #fff; }
        .status-suspended { background-color: #dc3545; color: #fff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card status-card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold">Registration Status</h2>
                            <p class="text-muted">Check your provider account status</p>
                        </div>
                        
                        <?php if ($message): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($provider): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Registration Found!</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-sm-4"><strong>Provider ID:</strong></div>
                                    <div class="col-sm-8"><?php echo htmlspecialchars($provider['provider_id']); ?></div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4"><strong>Business Name:</strong></div>
                                    <div class="col-sm-8"><?php echo htmlspecialchars($provider['business_name']); ?></div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4"><strong>Contact Person:</strong></div>
                                    <div class="col-sm-8"><?php echo htmlspecialchars($provider['contact_person']); ?></div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4"><strong>WhatsApp:</strong></div>
                                    <div class="col-sm-8"><?php echo htmlspecialchars($provider['whatsapp_number']); ?></div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4"><strong>Email:</strong></div>
                                    <div class="col-sm-8"><?php echo htmlspecialchars($provider['email'] ?: 'Not provided'); ?></div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4"><strong>Status:</strong></div>
                                    <div class="col-sm-8">
                                        <span class="badge status-<?php echo $provider['status']; ?>">
                                            <?php echo ucfirst($provider['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4"><strong>Registered:</strong></div>
                                    <div class="col-sm-8"><?php echo date('M j, Y g:i A', strtotime($provider['registration_date'])); ?></div>
                                </div>
                                
                                <hr>
                                <div class="text-center">
                                    <?php if ($provider['status'] === 'pending'): ?>
                                        <p class="mb-2"><i class="fas fa-clock text-warning me-2"></i>Your account is pending approval</p>
                                        <small class="text-muted">You will receive a WhatsApp notification once approved</small>
                                    <?php elseif ($provider['status'] === 'active'): ?>
                                        <p class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Your account is active</p>
                                        <a href="login.php" class="btn btn-success">
                                            <i class="fas fa-sign-in-alt me-2"></i>Login to Dashboard
                                        </a>
                                    <?php elseif ($provider['status'] === 'suspended'): ?>
                                        <p class="mb-2"><i class="fas fa-exclamation-triangle text-danger me-2"></i>Your account is suspended</p>
                                        <small class="text-muted">Please contact support for assistance</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="mb-4">
                            <div class="mb-3">
                                <label for="whatsapp" class="form-label">
                                    <i class="fab fa-whatsapp me-2"></i>WhatsApp Number
                                </label>
                                <input type="text" class="form-control" id="whatsapp" name="whatsapp" 
                                       placeholder="e.g., +*************" required
                                       value="<?php echo htmlspecialchars($_POST['whatsapp'] ?? ''); ?>">
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Check Status
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <a href="login.php" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>Back to Login
                            </a>
                            <a href="https://wa.me/*************" target="_blank" class="btn btn-outline-success">
                                <i class="fab fa-whatsapp me-2"></i>Contact Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
