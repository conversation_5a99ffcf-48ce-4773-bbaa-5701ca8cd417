package com.official.fivegfastvpn.pro;

import static android.content.Context.MODE_PRIVATE;

import android.content.Context;
import android.content.SharedPreferences;
//Developer :--<PERSON><PERSON> <PERSON><PERSON><PERSON>
public class ProConfig {


    public static final String all_month_id = "onemonth";
    public static final String all_threemonths_id = "threemonth";
    public static final String all_sixmonths_id = "sixmonth";
    public static final String all_yearly_id = "oneyear";


    public static boolean vip_subscription = false;
    public static boolean all_subscription = false;


    public static void setPremium(boolean b, Context context) {
        SharedPreferences.Editor editor = context.getSharedPreferences("premium", MODE_PRIVATE).edit();
        editor.putBoolean("premium", b);
        editor.apply();
    }

    public static boolean isPremium(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("premium", MODE_PRIVATE);
       return prefs.getBoolean("premium", false);
      // return true; // For testing purposes
    }

}

