package com.official.fivegfastvpn;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;
import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.QueryPurchasesParams;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.official.fivegfastvpn.ads.AdsHelper;
import com.official.fivegfastvpn.ads.AppOpenManager;
import com.official.fivegfastvpn.api.Const;
import com.official.fivegfastvpn.api.VpnApiService;
import com.official.fivegfastvpn.model.AppConfig;
import com.official.fivegfastvpn.model.Server;
import com.official.fivegfastvpn.pro.ProConfig;
import com.official.fivegfastvpn.utils.Pref;
import com.official.fivegfastvpn.utils.Utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

//Developer :--Md Sadrul Hasan Dider
public class SplashActivity extends AppCompatActivity {
    Pref pref;

    BillingClient billingClient;
    private LottieAnimationView lottieAnimationView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);


        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_splash_screen);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        lottieAnimationView = findViewById(R.id.animationView);

        pref = new Pref(SplashActivity.this);

        checkPro();
        getAll();

    }

    private void checkPro() {
        billingClient = BillingClient.newBuilder(this).enablePendingPurchases().setListener((billingResult, list) -> {
        }).build();
        final BillingClient finalBillingClient = billingClient;
        billingClient.startConnection(new BillingClientStateListener() {
            @Override
            public void onBillingServiceDisconnected() {
            }

            @Override
            public void onBillingSetupFinished(@NonNull BillingResult billingResult) {

                if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                    finalBillingClient.queryPurchasesAsync(QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.SUBS).build(), (billingResult1, list) -> {
                        if (billingResult1.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                            Log.d("testOffer", list.size() + " size");
                            if (list.size() > 0) {
                                ProConfig.setPremium(true, SplashActivity.this); // set 1 to activate premium feature
                                int i = 0;
                                for (Purchase purchase : list) {
                                    //Here you can manage each product, if you have multiple subscription
                                    Log.d("testOffer", purchase.getOriginalJson()); // Get to see the order information
                                    Log.d("testOffer", " index" + i);
                                    i++;
                                }
                            } else {
                                ProConfig.setPremium(false, SplashActivity.this); // set 0 to de-activate premium feature
                            }
                        }
                    });
                }
            }
        });

    }

    public void getAll() {
        // Show ProgressBar when starting to load servers
        lottieAnimationView.setVisibility(View.VISIBLE);

        // Use the new VpnApiService to get app configuration
        VpnApiService apiService = VpnApiService.getInstance(this);
        apiService.getAppConfig(getPackageName(), new VpnApiService.ApiCallback<AppConfig>() {
            @Override
            public void onSuccess(AppConfig config) {
                Log.d("ServerDebug", "App config loaded successfully");
                processAppConfig(config);
            }

            @Override
            public void onError(String error, int errorCode) {
                Log.e("ServerDebug", "Failed to load app config: " + error + " (Code: " + errorCode + ")");
                lottieAnimationView.setVisibility(View.GONE);
                Toast.makeText(SplashActivity.this, "Connection error: " + error, Toast.LENGTH_SHORT).show();
                proceedToMainActivity();
            }
        });
    }

    /**
     * Process the app configuration received from the API
     */
    private void processAppConfig(AppConfig config) {
        try {
            // Hide ProgressBar when data is loaded
            lottieAnimationView.setVisibility(View.VISIBLE);

            // Set ads configuration using the new config model
            AdsHelper.admob_id = config.getAdmobId();
            AdsHelper.admob_banner = config.getAdmobBanner();
            AdsHelper.admob_interstitial = config.getAdmobInterstitial();
            AdsHelper.admob_native = config.getAdmobNative();
            AdsHelper.admob_rewarded = config.getAdmobRewarded();
            AdsHelper.admob_open_ad = config.getAdmobOpenad();

            // Facebook ads
            AdsHelper.facebook_id = config.getFacebookId();
            AdsHelper.facebook_banner = config.getFacebookBanner();
            AdsHelper.facebook_interstitial = config.getFacebookInterstitial();
            AdsHelper.facebook_native = config.getFacebookNative();

            // Ad types and settings
            AdsHelper.banner_type = config.getBannerType();
            AdsHelper.interstitial_type = config.getInterstitialType();
            AdsHelper.native_type = config.getNativeType();
            AdsHelper.rewarded_type = config.getRewardedType();
            AdsHelper.reward_time = String.valueOf(config.getRewardTime());
            AdsHelper.isAds = !ProConfig.isPremium(this) && config.areAdsEnabled();
            AdsHelper.isOpenAdEnabled = config.getOpenadEnabled() == 1;

            // Convert servers list to JSON string for backward compatibility
            if (config.getServers() != null && !config.getServers().isEmpty()) {
                JSONArray serversArray = new JSONArray();
                for (Server server : config.getServers()) {
                    JSONObject serverJson = new JSONObject();
                    serverJson.put("id", server.getId());
                    serverJson.put("name", server.getCountry());
                    serverJson.put("username", server.getUsername());
                    serverJson.put("password", server.getPassword());
                    serverJson.put("configFile", server.getOvpn());
                    serverJson.put("flagURL", server.getFlagUrl());
                    serverJson.put("type", server.getType());
                    serverJson.put("pos", server.getPos());
                    serverJson.put("status", server.getStatus());
                    serversArray.put(serverJson);
                }
                Const.SERVERS = serversArray.toString();
                Log.d("ServerDebug", "Servers data loaded into Const.SERVERS, count: " + config.getServers().size());
            }

            // Set default server if needed
            if (pref.getServer().getOvpn().length() < 5) {
                Log.d("ServerDebug", "Setting single server as default");
                setSingleServer();
            }

            // CRITICAL FIX: Add timeout mechanism to prevent splash screen hanging
            // Set a maximum timeout for the entire splash process
            Handler timeoutHandler = new Handler();
            Runnable timeoutRunnable = new Runnable() {
                @Override
                public void run() {
                    Log.w("SplashActivity", "Splash timeout reached - forcing proceed to main activity");
                    if (!isFinishing()) {
                        proceedToMainActivity();
                    }
                }
            };
            timeoutHandler.postDelayed(timeoutRunnable, 10000); // 10 second timeout

            // CRITICAL FIX: Add small delay to ensure VPN state detection is reliable
            new Handler().postDelayed(() -> {
                // Handle app open ads - check if open ads are allowed
                if (AdsHelper.isOpenAdAllowed(this) && getApplication() instanceof VPNApplication) {
                    VPNApplication app = (VPNApplication) getApplication();
                    app.setAdLoadCallback(new AppOpenManager.OnAppOpenAdLoadCallback() {
                        @Override
                        public void onAdLoaded() {
                            // Cancel timeout since we're proceeding
                            timeoutHandler.removeCallbacks(timeoutRunnable);
                            if (!isFinishing()) {
                                app.showAdIfAvailable(SplashActivity.this);
                            }
                        }

                        @Override
                        public void onAdFailedToLoad(String error) {
                            Log.d("SplashActivity", "Open ad failed to load: " + error);
                            // Cancel timeout since we're proceeding
                            timeoutHandler.removeCallbacks(timeoutRunnable);
                            if (!isFinishing()) {
                                proceedToMainActivity();
                            }
                        }
                    });
                    app.initializeAppOpenAd();
                } else {
                    Log.d("SplashActivity", "Open ads not allowed - proceeding to main activity");
                    // Cancel timeout since we're proceeding
                    timeoutHandler.removeCallbacks(timeoutRunnable);
                    proceedToMainActivity();
                }
            }, 300); // Small delay to ensure VPN state detection is reliable

        } catch (Exception e) {
            Log.e("ServerDebug", "Error processing app config: " + e.getMessage(), e);
            Toast.makeText(this, "Something went wrong..", Toast.LENGTH_SHORT).show();
            proceedToMainActivity();
        }
    }

    private String hmacSha256(String data, String key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
            mac.init(secretKey);
            byte[] hash = mac.doFinal(data.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("Error generating HMAC", e);
        }
    }

    public void proceedToMainActivity() {
        // CRITICAL FIX: Check VPN state before proceeding to MainActivity
        // This prevents splash screen from getting stuck when VPN is already running
        try {
            Log.d("SplashActivity", "Checking VPN state before proceeding to MainActivity");

            // Check if VPN service is running
            boolean isVpnRunning = checkVpnServiceRunning();
            Log.d("SplashActivity", "VPN service running: " + isVpnRunning);

            Intent intent = new Intent(SplashActivity.this, MainActivity.class);

            // Add flags to indicate app is being launched from splash and VPN state
            intent.putExtra("FROM_SPLASH", true);
            intent.putExtra("VPN_WAS_RUNNING", isVpnRunning);

            // Use appropriate flags to ensure smooth transition
            if (isVpnRunning) {
                // If VPN is running, use flags that work well with existing service
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            } else {
                // Normal launch flags
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            }

            startActivity(intent);
            overridePendingTransition(R.anim.slide_in_right, R.anim.stay);
            finish();

        } catch (Exception e) {
            Log.e("SplashActivity", "Error checking VPN state, proceeding anyway", e);

            // Fallback to normal launch
            Intent intent = new Intent(SplashActivity.this, MainActivity.class);
            intent.putExtra("FROM_SPLASH", true);
            startActivity(intent);
            overridePendingTransition(R.anim.slide_in_right, R.anim.stay);
            finish();
        }
    }

    /**
     * Check if VPN service is currently running
     */
    private boolean checkVpnServiceRunning() {
        try {
            // Method 1: Check OpenVPN service status
            String vpnStatus = de.blinkt.openvpn.core.OpenVPNService.getStatus();
            if (vpnStatus != null && !vpnStatus.isEmpty() &&
                (vpnStatus.equals("CONNECTED") || vpnStatus.equals("CONNECTING"))) {
                Log.d("SplashActivity", "VPN detected via OpenVPN status: " + vpnStatus);
                return true;
            }

            // Method 2: Check saved VPN state
            Pref pref = new Pref(this);
            boolean savedVpnState = pref.getVpnConnectionState();
            if (savedVpnState) {
                Log.d("SplashActivity", "VPN detected via saved state");
                return true;
            }

            // Method 3: Check VPN permission state
            Intent vpnIntent = android.net.VpnService.prepare(this);
            // If intent is null, VPN permission is granted and might be active
            if (vpnIntent == null) {
                Log.d("SplashActivity", "VPN permission already granted, might be active");
                // This is not definitive but indicates potential VPN activity
            }

            return false;

        } catch (Exception e) {
            Log.e("SplashActivity", "Error checking VPN service status", e);
            return false;
        }
    }

    private void setSingleServer() {
        try {
            JSONArray jsonArray = new JSONArray(Const.SERVERS);
            
            // Create a list of free servers (type="1")
            java.util.List<JSONObject> freeServers = new java.util.ArrayList<>();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject server = jsonArray.getJSONObject(i);
                if (server.getString("type").contains("1")) {
                    freeServers.add(server);
                }
            }
            
            // Select a random server from the free servers list
            if (freeServers.size() > 0) {
                int randomIndex = new java.util.Random().nextInt(freeServers.size());
                JSONObject object = freeServers.get(randomIndex);
                
                Server server = new Server(object.getString("name"), Utils.imgUrl("flag/", object.getString("flagURL")), object.getString("configFile"), object.getString("username"), object.getString("password"));
                
                // Log the random server info
                Log.d("DefaultServer", "Setting random free server: " + object.getString("name"));
                Log.d("DefaultServer", "Server country: " + object.getString("name"));
                Log.d("DefaultServer", "Server config: " + object.getString("configFile").substring(0, Math.min(50, object.getString("configFile").length())) + "...");
                Log.d("DefaultServer", "Auth: " + object.getString("username") + " / " + object.getString("password"));
                Log.d("DefaultServer", "Selected random server " + (randomIndex + 1) + " of " + freeServers.size() + " available free servers");

                pref.saveServer(server);
            } else {
                // Fallback to first server if no free servers found
                JSONObject object = jsonArray.getJSONObject(0);
                Server server = new Server(object.getString("name"), Utils.imgUrl("flag/", object.getString("flagURL")), object.getString("configFile"), object.getString("username"), object.getString("password"));
                
                Log.d("DefaultServer", "No free servers found, using first server: " + object.getString("name"));
                pref.saveServer(server);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}