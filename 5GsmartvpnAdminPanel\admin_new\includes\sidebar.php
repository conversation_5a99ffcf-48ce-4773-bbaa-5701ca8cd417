<?php
/**
 * 5G Smart VPN Admin Panel - Sidebar Navigation
 */

// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);

// Define menu items
$menu_items = [
    [
        'type' => 'single',
        'title' => 'Dashboard',
        'icon' => 'ri-dashboard-3-line',
        'url' => 'index.php',
        'active' => $current_page === 'index.php'
    ],
    [
        'type' => 'divider',
        'title' => 'Management'
    ],
    [
        'type' => 'group',
        'title' => 'Server Management',
        'icon' => 'ri-server-line',
        'active' => in_array($current_page, ['servers.php', 'server-add.php', 'server-edit.php']),
        'items' => [
            [
                'title' => 'All Servers',
                'url' => 'servers.php',
                'active' => $current_page === 'servers.php'
            ],
            [
                'title' => 'Add Server',
                'url' => 'server-add.php',
                'active' => $current_page === 'server-add.php'
            ]
        ]
    ],
    [
        'type' => 'group',
        'title' => 'Advertising',
        'icon' => 'ri-advertisement-line',
        'active' => in_array($current_page, ['ads.php', 'custom-ads.php', 'custom-ads-packages.php', 'ad-analytics.php', 'payment-verification.php', 'ad-approval.php', 'customer-accounts.php', 'payment-methods.php', 'custom-ads-analytics.php', 'provider-registrations.php']),
        'items' => [
            [
                'title' => 'AdMob Settings',
                'url' => 'ads.php',
                'active' => $current_page === 'ads.php'
            ],
            [
                'title' => 'Custom Ads',
                'url' => 'custom-ads.php',
                'active' => $current_page === 'custom-ads.php'
            ],
            [
                'title' => 'Ad Packages',
                'url' => 'custom-ads-packages.php',
                'active' => $current_page === 'custom-ads-packages.php'
            ],
            [
                'title' => 'Ad Approval',
                'url' => 'ad-approval.php',
                'active' => $current_page === 'ad-approval.php'
            ],
            [
                'title' => 'Payment Verification',
                'url' => 'payment-verification.php',
                'active' => $current_page === 'payment-verification.php'
            ],
            [
                'title' => 'Customer Accounts',
                'url' => 'customer-accounts.php',
                'active' => $current_page === 'customer-accounts.php'
            ],
            [
                'title' => 'Payment Methods',
                'url' => 'payment-methods.php',
                'active' => $current_page === 'payment-methods.php'
            ],
            [
                'title' => 'Analytics',
                'url' => 'custom-ads-analytics.php',
                'active' => $current_page === 'custom-ads-analytics.php'
            ],
            [
                'title' => 'Provider Registrations',
                'url' => 'provider-registrations.php',
                'active' => $current_page === 'provider-registrations.php'
            ]
        ]
    ],
    [
        'type' => 'single',
        'title' => 'Notifications',
        'icon' => 'ri-notification-4-line',
        'url' => 'notifications.php',
        'active' => $current_page === 'notifications.php',
        'badge' => $stats['pending_notifications'] ?? 0
    ],
    [
        'type' => 'single',
        'title' => 'Contact Messages',
        'icon' => 'ri-mail-line',
        'url' => 'contacts.php',
        'active' => $current_page === 'contacts.php',
        'badge' => $stats['unread_contacts'] ?? 0
    ],
    [
        'type' => 'divider',
        'title' => 'System'
    ],
    [
        'type' => 'single',
        'title' => 'Settings',
        'icon' => 'ri-settings-3-line',
        'url' => 'settings.php',
        'active' => $current_page === 'settings.php'
    ],
    [
        'type' => 'single',
        'title' => 'Activity Logs',
        'icon' => 'ri-file-list-3-line',
        'url' => 'logs.php',
        'active' => $current_page === 'logs.php'
    ],
    [
        'type' => 'divider',
        'title' => 'Account'
    ],
    [
        'type' => 'single',
        'title' => 'Logout',
        'icon' => 'ri-logout-box-line',
        'url' => 'logout.php',
        'active' => false,
        'class' => 'logout-link'
    ]
];
?>

<aside class="admin-sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <div class="logo-icon">
                <img src="assets/images/logo-compact.svg" alt="5G Smart VPN" class="logo-image">
            </div>
            <div class="logo-text">
                <h2 class="logo-title">5G Smart VPN</h2>
                <p class="logo-subtitle">Admin Panel</p>
            </div>
        </div>

        <!-- Mobile Close Button -->
        <button class="sidebar-close" onclick="closeMobileSidebar()">
            <i class="ri-close-line"></i>
        </button>
    </div>

    <!-- Sidebar Navigation -->
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <?php foreach ($menu_items as $item): ?>
                <?php if ($item['type'] === 'divider'): ?>
                    <li class="nav-divider">
                        <span class="divider-text"><?php echo $item['title']; ?></span>
                    </li>

                <?php elseif ($item['type'] === 'single'): ?>
                    <li class="nav-item">
                        <?php if (isset($item['class']) && $item['class'] === 'logout-link'): ?>
                            <a href="<?php echo $item['url']; ?>" class="nav-link <?php echo $item['active'] ? 'active' : ''; ?> text-danger" onclick="return confirm('Are you sure you want to logout?')">
                                <i class="nav-icon <?php echo $item['icon']; ?>"></i>
                                <span class="nav-text"><?php echo $item['title']; ?></span>
                            </a>
                        <?php else: ?>
                            <a href="<?php echo $item['url']; ?>" class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>">
                                <i class="nav-icon <?php echo $item['icon']; ?>"></i>
                                <span class="nav-text"><?php echo $item['title']; ?></span>
                                <?php if (isset($item['badge']) && $item['badge'] > 0): ?>
                                    <span class="nav-badge"><?php echo $item['badge']; ?></span>
                                <?php endif; ?>
                            </a>
                        <?php endif; ?>
                    </li>

                <?php elseif ($item['type'] === 'group'): ?>
                    <li class="nav-item nav-group <?php echo $item['active'] ? 'active' : ''; ?>">
                        <a href="#" class="nav-link nav-toggle" onclick="toggleNavGroup(this)">
                            <i class="nav-icon <?php echo $item['icon']; ?>"></i>
                            <span class="nav-text"><?php echo $item['title']; ?></span>
                            <i class="nav-arrow ri-arrow-down-s-line"></i>
                        </a>
                        <ul class="nav-submenu <?php echo $item['active'] ? 'show' : ''; ?>">
                            <?php foreach ($item['items'] as $subitem): ?>
                                <li class="nav-subitem">
                                    <a href="<?php echo $subitem['url']; ?>"
                                       class="nav-sublink <?php echo $subitem['active'] ? 'active' : ''; ?>"
                                       <?php echo isset($subitem['target']) ? 'target="' . $subitem['target'] . '"' : ''; ?>>
                                        <span class="nav-subtext"><?php echo $subitem['title']; ?></span>
                                        <?php if (isset($subitem['icon'])): ?>
                                            <i class="nav-subicon <?php echo $subitem['icon']; ?>"></i>
                                        <?php endif; ?>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <!-- User Profile Card -->
        <div class="user-profile-card">
            <div class="user-avatar-section">
                <div class="user-avatar-wrapper">
                    <div class="user-avatar">
                        <i class="ri-user-line"></i>
                    </div>
                    <div class="user-status-indicator"></div>
                </div>
            </div>

            <div class="user-info-section">
                <div class="user-details">
                    <h4 class="user-name"><?php echo isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : 'test_admin'; ?></h4>
                    <p class="user-role">
                        <i class="ri-shield-check-line"></i>
                        Administrator
                    </p>
                </div>

                <div class="user-actions-row">
                    <a href="profile.php" class="action-btn profile-btn" title="My Profile">
                        <i class="ri-user-settings-line"></i>
                        <span>Profile</span>
                    </a>
                    <button class="action-btn theme-btn" onclick="toggleTheme()" title="Toggle Theme">
                        <i class="ri-moon-line theme-toggle-icon"></i>
                        <span class="theme-text">Dark</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="stat-item">
                <i class="ri-time-line"></i>
                <span>Online</span>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
                <i class="ri-code-line"></i>
                <span>v<?php echo ADMIN_PANEL_VERSION; ?></span>
            </div>
        </div>
    </div>
</aside>

<!-- Mobile Menu Toggle Button -->
<button class="mobile-menu-toggle" onclick="toggleMobileSidebar()">
    <i class="ri-menu-line"></i>
</button>

<script>
// Navigation group toggle
function toggleNavGroup(element) {
    const navItem = element.closest('.nav-group');
    const submenu = navItem.querySelector('.nav-submenu');
    const arrow = element.querySelector('.nav-arrow');

    if (navItem.classList.contains('active')) {
        navItem.classList.remove('active');
        submenu.classList.remove('show');
        arrow.style.transform = 'rotate(0deg)';
    } else {
        // Close other open groups
        document.querySelectorAll('.nav-group.active').forEach(group => {
            if (group !== navItem) {
                group.classList.remove('active');
                group.querySelector('.nav-submenu').classList.remove('show');
                group.querySelector('.nav-arrow').style.transform = 'rotate(0deg)';
            }
        });

        navItem.classList.add('active');
        submenu.classList.add('show');
        arrow.style.transform = 'rotate(180deg)';
    }
}

// Theme toggle function (moved here for sidebar access)
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('admin-theme', newTheme);

    // Update theme toggle button icon and text
    const themeToggleIcons = document.querySelectorAll('.theme-toggle-icon');
    const themeTexts = document.querySelectorAll('.theme-text');

    themeToggleIcons.forEach(icon => {
        icon.className = newTheme === 'dark' ? 'ri-sun-line theme-toggle-icon' : 'ri-moon-line theme-toggle-icon';
    });

    themeTexts.forEach(text => {
        text.textContent = newTheme === 'dark' ? 'Light' : 'Dark';
    });

    // Add smooth transition effect
    document.body.style.transition = 'all 0.3s ease';
    setTimeout(() => {
        document.body.style.transition = '';
    }, 300);
}

// Initialize navigation state
document.addEventListener('DOMContentLoaded', function() {
    // Auto-expand active groups
    document.querySelectorAll('.nav-group.active').forEach(group => {
        const arrow = group.querySelector('.nav-arrow');
        if (arrow) {
            arrow.style.transform = 'rotate(180deg)';
        }
    });

    // Initialize theme toggle button
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const themeToggleIcons = document.querySelectorAll('.theme-toggle-icon');
    const themeTexts = document.querySelectorAll('.theme-text');

    themeToggleIcons.forEach(icon => {
        icon.className = currentTheme === 'dark' ? 'ri-sun-line theme-toggle-icon' : 'ri-moon-line theme-toggle-icon';
    });

    themeTexts.forEach(text => {
        text.textContent = currentTheme === 'dark' ? 'Light' : 'Dark';
    });
});
</script>

<style>
.nav-subicon {
    margin-left: auto;
    font-size: 0.8rem;
    opacity: 0.7;
}

.nav-sublink {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-sublink:hover .nav-subicon {
    opacity: 1;
}
</style>
