package com.official.fivegfastvpn;

import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.GravityCompat;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.MutableLiveData;
import com.bumptech.glide.Glide;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.firebase.FirebaseApp;
import com.official.fivegfastvpn.activity.NotificationsActivity;
import com.official.fivegfastvpn.ads.AdCode;
import com.official.fivegfastvpn.ads.AdsHelper;
import com.official.fivegfastvpn.ads.Gdpr;
import com.official.fivegfastvpn.api.Const;
import com.official.fivegfastvpn.fragments.MainFragment;
import com.official.fivegfastvpn.model.Server;
import com.official.fivegfastvpn.pro.ProConfig;
import com.official.fivegfastvpn.utils.NotificationManager;
import com.official.fivegfastvpn.utils.Pref;
import com.official.fivegfastvpn.utils.Utils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

//Developer :--Md Sadrul Hasan Dider
public class MainActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {
    private static final int REQUEST_CODE = 101;

    public MutableLiveData<Server> defaultServer = new MutableLiveData<>();
    private Fragment fragment;
    private DrawerLayout drawer;
    private Pref preference;
    private boolean activateServer;
    private NotificationManager notificationManager;

    //update
    private static final int UPDATE_REQUEST_CODE = 22;
    private InAppUpdate inAppUpdate;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main_drawer);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(ContextCompat.getColor(this, R.color.FF5722));
        }
        inAppUpdate = new InAppUpdate(MainActivity.this);
        inAppUpdate.checkForAppUpdate();
        // Initialize Firebase
        FirebaseApp.initializeApp(this);
        preference = new Pref(this);
        defaultServer.setValue(preference.getServer());

        // Initialize notification manager
        notificationManager = NotificationManager.getInstance(this);

        // Handle notification click if app was opened from notification
        handleNotificationIntent(getIntent());

        // CRITICAL FIX: Handle VPN notification clicks
        handleVpnNotificationIntent(getIntent());

        // CRITICAL FIX: Handle splash screen launch with VPN state
        handleSplashLaunchIntent(getIntent());

        // Initialize Firebase topic subscriptions
        initializeFirebaseTopics();

        initializeAll();
        askNotificationPermission();

        openScreen(fragment, false);

        // Show app open ad only for non-premium users
        if (!ProConfig.isPremium(this) && getApplication() instanceof VPNApplication) {
            ((VPNApplication) getApplication()).showAdIfAvailable(MainActivity.this);
        }

        // Only show GDPR and load ads if not a premium user
        if (!ProConfig.isPremium(this)) {
            if (AdsHelper.isAds) {
                Gdpr gdpr = new Gdpr(MainActivity.this);
                gdpr.setGdpr();
            }
            AdCode.loadInt(MainActivity.this);
            AdCode.loadRew(MainActivity.this);
            fetchAndShowCustomAd();
        }
    }


    private void fetchAndShowCustomAd() {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            HttpURLConnection connection = null;
            try {
                // Compute timestamp and signature for the HMAC authentication
                String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
                String apiKey = Const.API_SECRET_KEY; // Use the correct API secret key
                String signature = hmacSha256(timestamp, apiKey);

                Log.d("MainActivity", "Fetching custom ads with timestamp: " + timestamp);
                Log.d("MainActivity", "Using API key: " + apiKey.substring(0, 10) + "...");

                String urlStr = Const.base + "api/custom_ads.php?timestamp=" + timestamp + "&signature=" + signature;
                Log.d("MainActivity", "Custom ads URL: " + urlStr);

                URL url = new URL(urlStr);
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);

                int responseCode = connection.getResponseCode();
                Log.d("MainActivity", "Custom ads response code: " + responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    String responseStr = response.toString();
                    Log.d("MainActivity", "Custom ads response: " + responseStr);

                    JSONArray jsonArray = new JSONArray(responseStr);
                    if (jsonArray.length() > 0) {
                        JSONObject ad = jsonArray.getJSONObject(0);
                        Log.d("MainActivity", "Custom ad data: " + ad.toString());

                        try {
                            String isOn = ad.getString("on");
                            String dateStart = ad.getString("date_start");
                            String dateEnd = ad.getString("date_end");
                            String title = ad.getString("title");
                            String image = ad.getString("image");
                            String text = ad.getString("text");
                            String url_str = ad.isNull("url") ? null : ad.getString("url");

                            // Extract new fields for enhanced custom ads
                            String urlType = ad.optString("url_type", "website");
                            String buttonText = ad.optString("button_text", "");

                            int adId = ad.getInt("id");

                            Log.d("MainActivity", "Showing custom ad: " + title + " (ID: " + adId + ")");
                            Log.d("MainActivity", "URL Type: " + urlType + ", Button Text: " + buttonText);

                            // Track custom ad view
                            com.official.fivegfastvpn.api.AdTracker.trackCustomAdView(MainActivity.this, adId);

                            runOnUiThread(() -> showCustomAdDialog(title, image, text, url_str, urlType, buttonText, adId));
                        } catch (JSONException e) {
                            Log.e("MainActivity", "Error parsing custom ad JSON", e);
                            runOnUiThread(() -> showCustomAdDialog("Welcome", "", "Thank you for using our app!", null));
                        }
                    } else {
                        Log.d("MainActivity", "No custom ads found in response");
                        runOnUiThread(() -> showCustomAdDialog("Welcome", "", "Thank you for using our app!", null));
                    }
                } else {
                    // Read error response
                    BufferedReader errorReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                    StringBuilder errorResponse = new StringBuilder();
                    String errorLine;
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorResponse.append(errorLine);
                    }
                    errorReader.close();

                    Log.e("MainActivity", "Custom ads API error: " + responseCode + " - " + errorResponse.toString());
                    runOnUiThread(() -> showCustomAdDialog("Welcome", "", "Thank you for using our app!", null));
                }
            } catch (Exception e) {
                Log.e("MainActivity", "Exception while fetching custom ads", e);
                e.printStackTrace();
                runOnUiThread(() -> showCustomAdDialog("Welcome", "", "Thank you for using our app!", null));
            } finally {
                if (connection != null) {
                    connection.disconnect();
                }
            }
        });
        executor.shutdown();
    }

    private String hmacSha256(String data, String key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(data.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("Failed to calculate hmac-sha256", e);
        }
    }

    // Enhanced method for custom ads with tracking and smart button text
    private void showCustomAdDialog(String title, String image, String text, String url, String urlType, String buttonText, int adId) {
        showCustomAdDialogInternal(title, image, text, url, urlType, buttonText, adId);
    }

    // Overloaded method for custom ads with tracking (legacy support)
    private void showCustomAdDialog(String title, String image, String text, String url, int adId) {
        showCustomAdDialogInternal(title, image, text, url, "website", "", adId);
    }

    // Legacy method for fallback cases
    private void showCustomAdDialog(String title, String image, String text, String url) {
        showCustomAdDialogInternal(title, image, text, url, "website", "", -1);
    }

    private void showCustomAdDialogInternal(String title, String image, String text, String url, String urlType, String buttonText, int adId) {
        Dialog dialog = new Dialog(this);
        dialog.setContentView(R.layout.dialog_custom_ad);
        dialog.setCancelable(false);

        // Set dialog width to match parent
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            layoutParams.copyFrom(window.getAttributes());
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;

            // Set maximum height to 80% of screen height
            DisplayMetrics displayMetrics = new DisplayMetrics();
            getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
            int maxHeight = (int) (displayMetrics.heightPixels * 0.8);
            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
            window.setAttributes(layoutParams);

            // Set maximum height for ScrollView
            View scrollContainer = dialog.findViewById(R.id.scroll_container);
            if (scrollContainer instanceof ScrollView) {
                ViewGroup.LayoutParams scrollParams = scrollContainer.getLayoutParams();
                scrollParams.height = maxHeight - (int) (200 * getResources().getDisplayMetrics().density); // Subtract space for title and buttons
                scrollContainer.setLayoutParams(scrollParams);
            }
        }

        TextView titleTv = dialog.findViewById(R.id.dialog_title);
        ImageView imageView = dialog.findViewById(R.id.dialog_image);
        TextView descTv = dialog.findViewById(R.id.dialog_description);
        Button closeBtn = dialog.findViewById(R.id.dialog_close_button);
        Button goToBtn = dialog.findViewById(R.id.dialog_goto_button);

        titleTv.setText(title);
        descTv.setText(text);

        // Load image using Glide
        Glide.with(this)
            .load(Const.base + image)
            .into(imageView);

        closeBtn.setOnClickListener(v -> dialog.dismiss());

        if (url != null) {
            goToBtn.setVisibility(View.VISIBLE);

            // Set smart button text based on URL type and custom text
            String finalButtonText = com.official.fivegfastvpn.utils.UrlDetectionUtils.getButtonTextFromType(urlType, buttonText);
            if (finalButtonText.isEmpty()) {
                finalButtonText = com.official.fivegfastvpn.utils.UrlDetectionUtils.getButtonText(url, buttonText);
            }
            goToBtn.setText(finalButtonText);

            Log.d("MainActivity", "Setting button text to: " + finalButtonText + " (URL Type: " + urlType + ", Custom: " + buttonText + ")");

            // Set equal width for both buttons with margins
            int margin = (int) (8 * getResources().getDisplayMetrics().density); // 8dp margin

            LinearLayout.LayoutParams closeParams = new LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1.0f
            );
            closeParams.setMarginEnd(margin); // Add right margin to close button
            closeBtn.setLayoutParams(closeParams);

            LinearLayout.LayoutParams gotoParams = new LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1.0f
            );
            gotoParams.setMarginStart(margin); // Add left margin to goto button
            goToBtn.setLayoutParams(gotoParams);

            goToBtn.setOnClickListener(v -> {
                // Track custom ad click if we have a valid adId
                if (adId > 0) {
                    com.official.fivegfastvpn.api.AdTracker.trackCustomAdClick(MainActivity.this, adId);
                    Log.d("MainActivity", "Tracked custom ad click for ID: " + adId);
                }

                try {
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    startActivity(intent);
                } catch (ActivityNotFoundException e) {
                    e.printStackTrace();
                }
                dialog.dismiss();
            });
        } else {
            goToBtn.setVisibility(View.GONE);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1.0f
            );
            int margin = (int) (16 * getResources().getDisplayMetrics().density); // 16dp in pixels
            params.setMargins(margin, 0, margin, 0);
            closeBtn.setLayoutParams(params);
        }

        dialog.show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        inAppUpdate.onResume();

        // Sync notifications when app comes to foreground
        if (notificationManager != null && notificationManager.isCacheExpired()) {
            notificationManager.syncNotifications(new NotificationManager.SyncCallback() {
                @Override
                public void onSyncComplete(boolean success, int count) {
                    if (success) {
                        Log.d("MainActivity", "Synced " + count + " notifications");
                    }
                }
            });
        }

        // CRITICAL FIX: Check for VPN state changes when app resumes
        checkVpnStateOnResume();
    }

    /**
     * Check VPN state when app resumes to handle cases where VPN was disconnected
     * while app was in background or killed from recent apps
     */
    private void checkVpnStateOnResume() {
        try {
            // Get current fragment
            Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.fragment_container);

            // If we're on the main fragment, trigger VPN state detection
            if (currentFragment instanceof MainFragment) {
                Log.d("MainActivity", "App resumed on MainFragment, checking VPN state");
                // The MainFragment will handle state detection in its own lifecycle
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error checking VPN state on resume", e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        inAppUpdate.onDestroy();
    }

    private void initializeAll() {
        drawer = findViewById(R.id.drawer_layout);
        NavigationView navigationView = findViewById(R.id.nav_view);
        navigationView.setNavigationItemSelectedListener(this);

        fragment = new MainFragment();
    }

    public void openCloseDrawer() {
        if (drawer.isDrawerOpen(GravityCompat.START)) {
            drawer.closeDrawer(GravityCompat.START, true);
        } else {
            drawer.openDrawer(GravityCompat.START, true);
        }
    }

    void openScreen(Fragment fragmentClass, boolean addToBackStack) {
        FragmentManager manager = getSupportFragmentManager();
        try {
            FragmentTransaction transaction = manager.beginTransaction();
            if (addToBackStack) transaction.addToBackStack(fragmentClass.getTag());
            transaction.replace(R.id.container, fragmentClass);
            transaction.commitAllowingStateLoss();
            manager.executePendingTransactions();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onStop() {
        if (defaultServer.getValue() != null) {
            preference.saveServer(defaultServer.getValue());
        }
        super.onStop();
    }

    @Override
    public boolean onNavigationItemSelected(MenuItem item) {
        int id = item.getItemId();
//        if (id == R.id.nav_pro) {
//            startActivity(new Intent(MainActivity.this, PremiumActivity.class));
//        }else if (id == R.id.nav_server) {
//            startActivity(new Intent(MainActivity.this, ServersActivity.class));
//        }
//        else
        if (id == R.id.improve) {
            showContactDialog();
        }else if (id == R.id.nav_notifications) {
            startActivity(new Intent(MainActivity.this, NotificationsActivity.class));
        }
        else if (id == R.id.nav_share) {
            Intent sharingIntent = new Intent(Intent.ACTION_SEND);
            sharingIntent.setType("text/plain");
            sharingIntent.putExtra(Intent.EXTRA_TEXT, "https://play.google.com/store/apps/details?id=" + getPackageName());
            sharingIntent.putExtra(Intent.EXTRA_SUBJECT, "Subject");
            startActivity(Intent.createChooser(sharingIntent, ""));
        } else if (id == R.id.nav_rate) {
            final String appPackageName = getPackageName();
            try {
                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=" + appPackageName)));
            } catch (ActivityNotFoundException anfe) {
                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + appPackageName)));
            }
        } else if (id == R.id.nav_policy) {
            Utils.openLink(Const.policy, MainActivity.this);
        } else if (id == R.id.nav_about) {
            showAboutDialog();
        }
        drawer.closeDrawer(GravityCompat.START);
        return true;
    }

    private void showContactDialog() {
        // Create the dialog
        Dialog contactDialog = new Dialog(this);
        contactDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        contactDialog.setContentView(R.layout.dialog_contact_us);

        // Make dialog background transparent
        if (contactDialog.getWindow() != null) {
            contactDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            contactDialog.getWindow().setLayout(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.WRAP_CONTENT
            );
        }

        // Set up email card click listener
        View emailCard = contactDialog.findViewById(R.id.email_card);
        emailCard.setOnClickListener(v -> {
            sendImprovementEmail();
            contactDialog.dismiss();
        });

        // Set up buttons
        Button cancelButton = contactDialog.findViewById(R.id.contact_cancel_button);
        Button sendButton = contactDialog.findViewById(R.id.contact_send_button);

        cancelButton.setOnClickListener(v -> contactDialog.dismiss());

        sendButton.setOnClickListener(v -> {
            // Get input values
            TextInputEditText nameInput = contactDialog.findViewById(R.id.name_input);
            TextInputEditText emailInput = contactDialog.findViewById(R.id.email_input);
            TextInputEditText messageInput = contactDialog.findViewById(R.id.message_input);

            String name = nameInput.getText() != null ? nameInput.getText().toString() : "";
            String email = emailInput.getText() != null ? emailInput.getText().toString() : "";
            String message = messageInput.getText() != null ? messageInput.getText().toString() : "";

            // No validation needed - empty fields will be handled in sendFeedbackEmail
            // Just make sure at least one field has content
            if (name.trim().isEmpty() && email.trim().isEmpty() && message.trim().isEmpty()) {
                Toast.makeText(this, "Please fill in at least one field", Toast.LENGTH_SHORT).show();
                return;
            }

            // Send feedback
            sendFeedbackEmail(name, email, message);

            // Show success message and dismiss dialog
            Toast.makeText(this, "Thank you for your feedback!", Toast.LENGTH_SHORT).show();
            contactDialog.dismiss();
        });

        // Show the dialog
        contactDialog.show();
    }
    private void sendFeedbackEmail(String name, String email, String message) {
        // Use a fixed email address to ensure messages go to the right place
        final String DEVELOPER_EMAIL = "<EMAIL>";

        // Handle empty fields by replacing with "Empty" text
        String userName = (name == null || name.trim().isEmpty()) ? "Empty" : name.trim();
        String userEmail = (email == null || email.trim().isEmpty()) ? "Empty" : email.trim();
        String userMessage = (message == null || message.trim().isEmpty()) ? "Empty" : message.trim();

        try {
            // Create email intent with proper formatting
            Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
            emailIntent.setData(Uri.parse("mailto:" + DEVELOPER_EMAIL));

            // Set email subject with app name for better organization
            emailIntent.putExtra(Intent.EXTRA_SUBJECT, "5G SMART VPN Feedback from " + userName);

            // Format the email body in a clean, readable format
            String emailBody =
                    "Name: " + userName + "\n\n" +
                            "Email: " + userEmail + "\n\n" +
                            "Message:\n" + userMessage + "\n\n" +
                            "Device: " + Build.MANUFACTURER + " " + Build.MODEL + "\n" +
                            "Android version: " + Build.VERSION.RELEASE + "\n" +
                            "App version: " + getAppVersion() + "\n\n" +
                            "Sent from 5G SMART VPN app";

            emailIntent.putExtra(Intent.EXTRA_TEXT, emailBody);

            // Show email app chooser
            startActivity(Intent.createChooser(emailIntent, "Send feedback via email"));

            // Show success message
            Toast.makeText(this, "Opening email app...", Toast.LENGTH_SHORT).show();
        } catch (ActivityNotFoundException e) {
            // Handle case where no email app is installed
            Toast.makeText(this, "No email app found. Please install an email app to send feedback.", Toast.LENGTH_LONG).show();
            Log.e("ContactDialog", "No email app found: " + e.getMessage());
        } catch (Exception e) {
            // Handle any other unexpected errors
            Toast.makeText(this, "Error sending email. Please try again later.", Toast.LENGTH_SHORT).show();
            Log.e("ContactDialog", "Error sending email: " + e.getMessage());
        }
    }

    private void sendImprovementEmail() {
        // Use a fixed email address to ensure messages go to the right place
        final String DEVELOPER_EMAIL = "<EMAIL>";

        try {
            // Create email intent with proper formatting
            Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
            emailIntent.setData(Uri.parse("mailto:" + DEVELOPER_EMAIL));

            // Set email subject with app name for better organization
            emailIntent.putExtra(Intent.EXTRA_SUBJECT, "5G SMART VPN Quick Feedback");

            // Pre-fill the email body with a template that matches the contact form format
            String emailBody =
                    "Name: [Your Name]\n\n" +
                            "Email: [Your Email]\n\n" +
                            "Message:\n" +
                            "[Your feedback here]\n\n" +
                            "Device: " + Build.MANUFACTURER + " " + Build.MODEL + "\n" +
                            "Android version: " + Build.VERSION.RELEASE + "\n" +
                            "App version: " + getAppVersion() + "\n\n" +
                            "Sent from 5G SMART VPN app";

            emailIntent.putExtra(Intent.EXTRA_TEXT, emailBody);

            // Show email app chooser
            startActivity(Intent.createChooser(emailIntent, "Send feedback via email"));

            // Show success message
            Toast.makeText(this, "Opening email app...", Toast.LENGTH_SHORT).show();
        } catch (ActivityNotFoundException e) {
            // Handle case where no email app is installed
            Toast.makeText(this, "No email app found. Please install an email app to send feedback.", Toast.LENGTH_LONG).show();
            Log.e("ContactDialog", "No email app found: " + e.getMessage());
        } catch (Exception e) {
            // Handle any other unexpected errors
            Toast.makeText(this, "Error sending email. Please try again later.", Toast.LENGTH_SHORT).show();
            Log.e("ContactDialog", "Error sending email: " + e.getMessage());
        }
    }


    private String getAppVersion() {
        try {
            PackageInfo pInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            return pInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            return "Unknown";
        }
    }
    private void showAboutDialog() {
        // Create the dialog
        Dialog aboutDialog = new Dialog(this);
        aboutDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        aboutDialog.setContentView(R.layout.dialog_about_us);

        // Make dialog background transparent
        if (aboutDialog.getWindow() != null) {
            aboutDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            aboutDialog.getWindow().setLayout(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.WRAP_CONTENT
            );
        }

        // Set app version
        TextView versionTextView = aboutDialog.findViewById(R.id.about_app_version);
        try {
            PackageInfo pInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            String versionName = pInfo.versionName;
            versionTextView.setText("Version " + versionName);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            versionTextView.setText("Version Unknown");
        }

        // Set close button click listener
        Button closeButton = aboutDialog.findViewById(R.id.about_close_button);
        closeButton.setOnClickListener(v -> aboutDialog.dismiss());

        // Show the dialog
        aboutDialog.show();
    }




    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK) {
            if (requestCode == REQUEST_CODE && data != null) {
                activateServer = true;
                Server server = data.getParcelableExtra("server");
                defaultServer.postValue(server);
            }
        }
        if (requestCode == UPDATE_REQUEST_CODE) {
            if (resultCode != RESULT_OK) {

                Log.d("updateResult", "onActivityResult: " + resultCode);
                // If the update is cancelled or fails,
                // you can request to start the update again.
            }
        }

    }

    //=======================Push Notifications==========================================
    private final ActivityResultLauncher<String> requestPermissionLauncher =
            registerForActivityResult(new ActivityResultContracts.RequestPermission(), isGranted -> {
                if (isGranted) {


                } else {


                }
            });
    private void askNotificationPermission() {
        // This is only necessary for API level >= 33 (TIRAMISU)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS) ==
                    PackageManager.PERMISSION_GRANTED) {


                // FCM SDK (and your app) can post notifications.


            } else if (shouldShowRequestPermissionRationale(android.Manifest.permission.POST_NOTIFICATIONS)) {


                new MaterialAlertDialogBuilder(MainActivity.this)
                        .setTitle("Notifications Permission")
                        .setMessage("Please Allow Notifications")
                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {

                                requestPermissionLauncher.launch(android.Manifest.permission.POST_NOTIFICATIONS);

                            }
                        })
                        .setNegativeButton("No", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {

                            }
                        })
                        .create()
                        .show();


            } else {


                // Directly ask for the permission
                requestPermissionLauncher.launch(android.Manifest.permission.POST_NOTIFICATIONS);


            }
        }
    }


    //=======================Push Notifications==========================================



    public boolean isActivateServer() {
        return activateServer;
    }

    /**
     * Handle notification click intent
     */
    private void handleNotificationIntent(Intent intent) {
        if (intent != null && notificationManager != null) {
            notificationManager.handleNotificationClick(intent);
        }
    }

    /**
     * Initialize Firebase topic subscriptions for notifications
     */
    private void initializeFirebaseTopics() {
        // Subscribe to general topic for all users
        com.google.firebase.messaging.FirebaseMessaging.getInstance().subscribeToTopic("all")
                .addOnCompleteListener(task -> {
                    String msg = "Subscribed to 'all' topic";
                    if (!task.isSuccessful()) {
                        msg = "Failed to subscribe to 'all' topic";
                    }
                    Log.d("MainActivity", msg);
                });

        // Subscribe to app-specific topic
        com.google.firebase.messaging.FirebaseMessaging.getInstance().subscribeToTopic("vpn_users")
                .addOnCompleteListener(task -> {
                    String msg = "Subscribed to 'vpn_users' topic";
                    if (!task.isSuccessful()) {
                        msg = "Failed to subscribe to 'vpn_users' topic";
                    }
                    Log.d("MainActivity", msg);
                });
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleNotificationIntent(intent);

        // CRITICAL FIX: Handle VPN notification clicks in onNewIntent
        handleVpnNotificationIntent(intent);

        // CRITICAL FIX: Handle splash screen launch with VPN state in onNewIntent
        handleSplashLaunchIntent(intent);
    }

    /**
     * Handle VPN notification click intent
     */
    private void handleVpnNotificationIntent(Intent intent) {
        if (intent != null && intent.getBooleanExtra("FROM_VPN_NOTIFICATION", false)) {
            Log.d("MainActivity", "Handling VPN notification click");

            // Navigate to main fragment (VPN interface)
            openScreen(new MainFragment(), false);

            // Clear the intent extra to prevent repeated handling
            intent.removeExtra("FROM_VPN_NOTIFICATION");
        }
    }

    /**
     * CRITICAL FIX: Handle splash screen launch intent with VPN state information
     */
    private void handleSplashLaunchIntent(Intent intent) {
        if (intent != null && intent.getBooleanExtra("FROM_SPLASH", false)) {
            Log.d("MainActivity", "Handling launch from splash screen");

            boolean vpnWasRunning = intent.getBooleanExtra("VPN_WAS_RUNNING", false);
            Log.d("MainActivity", "VPN was running when splash launched: " + vpnWasRunning);

            if (vpnWasRunning) {
                // VPN was already running, ensure MainFragment detects and restores state properly
                Log.d("MainActivity", "VPN was running, triggering state restoration");

                // Get current fragment and trigger VPN state detection if it's MainFragment
                Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.fragment_container);
                if (currentFragment instanceof MainFragment) {
                    Log.d("MainActivity", "Current fragment is MainFragment, triggering VPN state detection");
                    // The MainFragment will handle state detection in its lifecycle methods
                    // We just need to ensure it knows to check state
                } else {
                    Log.d("MainActivity", "Current fragment is not MainFragment, navigating to MainFragment");
                    // Navigate to MainFragment to handle VPN state
                    openScreen(new MainFragment(), false);
                }
            }

            // Clear the intent extras to prevent repeated handling
            intent.removeExtra("FROM_SPLASH");
            intent.removeExtra("VPN_WAS_RUNNING");
        }
    }

}
