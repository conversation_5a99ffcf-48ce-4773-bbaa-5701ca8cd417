<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Compatibility Test - Admin Panel Sidebar</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- Main CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
    
    <style>
        /* Test-specific styles */
        .test-container {
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .test-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .browser-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-results {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #ddd;
        }
        
        .test-pass {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }
        
        .test-fail {
            background: #ffeaea;
            border-left-color: #f44336;
        }
        
        .test-warning {
            background: #fff3e0;
            border-left-color: #ff9800;
        }
    </style>
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="logo-icon">
                        <i class="ri-shield-check-line" style="font-size: 2rem; color: #3b82f6;"></i>
                    </div>
                    <div class="logo-text">
                        <h2 class="logo-title">5G Smart VPN</h2>
                        <p class="logo-subtitle">Admin Panel</p>
                    </div>
                </div>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-divider">
                        <span class="divider-text">Management</span>
                    </li>
                    
                    <li class="nav-item nav-group active">
                        <a href="#" class="nav-link nav-toggle" onclick="toggleNavGroup(this)">
                            <i class="nav-icon ri-advertisement-line"></i>
                            <span class="nav-text">Advertising</span>
                            <i class="nav-arrow ri-arrow-down-s-line"></i>
                        </a>
                        <ul class="nav-submenu show">
                            <li class="nav-subitem">
                                <a href="ads.php" class="nav-sublink">
                                    <span class="nav-subtext">AdMob Settings</span>
                                </a>
                            </li>
                            <li class="nav-subitem">
                                <a href="custom-ads.php" class="nav-sublink">
                                    <span class="nav-subtext">Custom Ads</span>
                                </a>
                            </li>
                            <li class="nav-subitem">
                                <a href="provider-registrations.php" class="nav-sublink active" id="provider-registrations-link">
                                    <span class="nav-subtext">Provider Registrations</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="test-container">
                <div class="test-header">
                    <h1>Browser Compatibility Test</h1>
                    <p>Testing sidebar navigation visibility across different browsers</p>
                </div>
                
                <div class="browser-info" id="browser-info">
                    <h3>Browser Information</h3>
                    <p id="user-agent"></p>
                </div>
                
                <div class="test-results">
                    <h3>Test Results</h3>
                    <div id="test-results"></div>
                    
                    <button onclick="runTests()" class="btn btn-primary" style="margin-top: 20px;">
                        <i class="ri-refresh-line me-1"></i>Run Tests
                    </button>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Browser compatibility test functions
        function runTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '';
            
            const tests = [
                testSidebarVisibility,
                testProviderRegistrationsLink,
                testNavGroupToggle,
                testCSSSupport,
                testJavaScriptFeatures
            ];
            
            tests.forEach(test => {
                const result = test();
                const div = document.createElement('div');
                div.className = `test-item test-${result.status}`;
                div.innerHTML = `<strong>${result.name}:</strong> ${result.message}`;
                results.appendChild(div);
            });
        }
        
        function testSidebarVisibility() {
            const sidebar = document.querySelector('.admin-sidebar');
            const isVisible = sidebar && window.getComputedStyle(sidebar).display !== 'none';
            
            return {
                name: 'Sidebar Visibility',
                status: isVisible ? 'pass' : 'fail',
                message: isVisible ? 'Sidebar is visible' : 'Sidebar is not visible'
            };
        }
        
        function testProviderRegistrationsLink() {
            const link = document.getElementById('provider-registrations-link');
            const isVisible = link && window.getComputedStyle(link).display !== 'none';
            const isClickable = link && link.offsetParent !== null;
            
            return {
                name: 'Provider Registrations Link',
                status: (isVisible && isClickable) ? 'pass' : 'fail',
                message: (isVisible && isClickable) ? 'Link is visible and clickable' : 'Link has visibility issues'
            };
        }
        
        function testNavGroupToggle() {
            try {
                const navGroup = document.querySelector('.nav-group');
                const hasActiveClass = navGroup && navGroup.classList.contains('active');
                
                return {
                    name: 'Navigation Group Toggle',
                    status: hasActiveClass ? 'pass' : 'warning',
                    message: hasActiveClass ? 'Nav group toggle working' : 'Nav group may have toggle issues'
                };
            } catch (e) {
                return {
                    name: 'Navigation Group Toggle',
                    status: 'fail',
                    message: 'Error testing nav group: ' + e.message
                };
            }
        }
        
        function testCSSSupport() {
            const features = [];
            
            // Test CSS custom properties
            if (CSS && CSS.supports && CSS.supports('color', 'var(--test)')) {
                features.push('CSS Variables: ✓');
            } else {
                features.push('CSS Variables: ✗ (using fallbacks)');
            }
            
            // Test flexbox
            if (CSS && CSS.supports && CSS.supports('display', 'flex')) {
                features.push('Flexbox: ✓');
            } else {
                features.push('Flexbox: ✗');
            }
            
            // Test transforms
            if (CSS && CSS.supports && CSS.supports('transform', 'rotate(0deg)')) {
                features.push('Transforms: ✓');
            } else {
                features.push('Transforms: ✗');
            }
            
            return {
                name: 'CSS Feature Support',
                status: 'pass',
                message: features.join(', ')
            };
        }
        
        function testJavaScriptFeatures() {
            const features = [];
            
            // Test querySelector
            features.push('querySelector: ' + (document.querySelector ? '✓' : '✗'));
            
            // Test classList
            features.push('classList: ' + (document.body.classList ? '✓' : '✗'));
            
            // Test addEventListener
            features.push('addEventListener: ' + (document.addEventListener ? '✓' : '✗'));
            
            return {
                name: 'JavaScript Feature Support',
                status: 'pass',
                message: features.join(', ')
            };
        }
        
        // Display browser information
        function displayBrowserInfo() {
            const userAgent = document.getElementById('user-agent');
            userAgent.textContent = navigator.userAgent;
        }
        
        // Navigation group toggle function (copied from sidebar.php)
        function toggleNavGroup(element) {
            var navItem = element;
            while (navItem && navItem.className.indexOf('nav-group') === -1) {
                navItem = navItem.parentNode;
            }
            
            if (!navItem) return;
            
            var submenu = navItem.querySelector ? navItem.querySelector('.nav-submenu') : 
                          navItem.getElementsByClassName('nav-submenu')[0];
            var arrow = element.querySelector ? element.querySelector('.nav-arrow') : 
                        element.getElementsByClassName('nav-arrow')[0];

            var isActive = navItem.className && navItem.className.indexOf('active') !== -1;

            if (isActive) {
                if (navItem.classList) {
                    navItem.classList.remove('active');
                    if (submenu && submenu.classList) submenu.classList.remove('show');
                } else {
                    navItem.className = navItem.className.replace(/\bactive\b/g, '');
                    if (submenu) submenu.className = submenu.className.replace(/\bshow\b/g, '');
                }
                if (arrow) {
                    arrow.style.transform = 'rotate(0deg)';
                    arrow.style.webkitTransform = 'rotate(0deg)';
                }
            } else {
                if (navItem.classList) {
                    navItem.classList.add('active');
                    if (submenu && submenu.classList) submenu.classList.add('show');
                } else {
                    navItem.className += ' active';
                    if (submenu) submenu.className += ' show';
                }
                if (arrow) {
                    arrow.style.transform = 'rotate(180deg)';
                    arrow.style.webkitTransform = 'rotate(180deg)';
                }
            }
        }
        
        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            displayBrowserInfo();
            runTests();
        });
        
        // Fallback for older browsers
        if (!document.addEventListener) {
            window.onload = function() {
                displayBrowserInfo();
                runTests();
            };
        }
    </script>
</body>
</html>
