<?php
/**
 * Provider Registrations Management
 * Admin interface to view and manage provider registrations
 */

session_start();
require_once 'includes/config.php';

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Provider Registrations';

// Add Bootstrap CSS and JS for proper dropdown functionality
$additional_css = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'
];
$additional_js = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
];

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_status') {
        $provider_id = $_POST['provider_id'];
        $new_status = $_POST['status'];
        
        $stmt = $conn->prepare("UPDATE custom_ads_providers SET status = ? WHERE id = ?");
        $stmt->bind_param("si", $new_status, $provider_id);
        
        if ($stmt->execute()) {
            $success_message = "Provider status updated successfully.";
        } else {
            $error_message = "Failed to update provider status.";
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query
$where_conditions = [];
$params = [];
$types = '';

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
    $types .= 's';
}

if (!empty($search)) {
    $where_conditions[] = "(business_name LIKE ? OR contact_person LIKE ? OR whatsapp_number LIKE ? OR email LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    $types .= 'ssss';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get providers
$query = "SELECT * FROM custom_ads_providers $where_clause ORDER BY registration_date DESC";
$stmt = $conn->prepare($query);

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$providers = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Get statistics
$stats = [
    'total' => $conn->query("SELECT COUNT(*) as count FROM custom_ads_providers")->fetch_assoc()['count'],
    'pending' => $conn->query("SELECT COUNT(*) as count FROM custom_ads_providers WHERE status = 'pending'")->fetch_assoc()['count'],
    'active' => $conn->query("SELECT COUNT(*) as count FROM custom_ads_providers WHERE status = 'active'")->fetch_assoc()['count'],
    'suspended' => $conn->query("SELECT COUNT(*) as count FROM custom_ads_providers WHERE status = 'suspended'")->fetch_assoc()['count']
];

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Provider Registrations</h1>
                <p class="page-subtitle">Manage custom ads provider registrations and approvals</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="ri-refresh-line me-1"></i>Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="content-body">
            <div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo $stats['total']; ?></h4>
                                            <p class="mb-0">Total Providers</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo $stats['pending']; ?></h4>
                                            <p class="mb-0">Pending Approval</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo $stats['active']; ?></h4>
                                            <p class="mb-0">Active Providers</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo $stats['suspended']; ?></h4>
                                            <p class="mb-0">Suspended</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-ban fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <input type="text" class="form-control me-2" name="search" 
                                       placeholder="Search providers..." value="<?php echo htmlspecialchars($search); ?>">
                                <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form method="GET" class="d-flex justify-content-end">
                                <select name="status" class="form-select me-2" style="width: auto;" onchange="this.form.submit()">
                                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                </select>
                                <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                            </form>
                        </div>
                    </div>
                    
                    <!-- Providers Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Provider ID</th>
                                    <th>Business Info</th>
                                    <th>Contact</th>
                                    <th>Status</th>
                                    <th>Registration Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($providers)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No providers found</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($providers as $provider): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($provider['provider_id']); ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($provider['business_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($provider['contact_person']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <i class="fab fa-whatsapp text-success me-1"></i>
                                                    <?php echo htmlspecialchars($provider['whatsapp_number']); ?><br>
                                                    <?php if ($provider['email']): ?>
                                                        <i class="fas fa-envelope text-muted me-1"></i>
                                                        <small><?php echo htmlspecialchars($provider['email']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $provider['status'] === 'active' ? 'success' : 
                                                        ($provider['status'] === 'pending' ? 'warning' : 'danger'); 
                                                ?>">
                                                    <?php echo ucfirst($provider['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y', strtotime($provider['registration_date'])); ?><br>
                                                <small class="text-muted"><?php echo date('g:i A', strtotime($provider['registration_date'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                                            data-bs-toggle="dropdown">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if ($provider['status'] === 'pending'): ?>
                                                            <li>
                                                                <form method="POST" style="display: inline;">
                                                                    <input type="hidden" name="action" value="update_status">
                                                                    <input type="hidden" name="provider_id" value="<?php echo $provider['id']; ?>">
                                                                    <input type="hidden" name="status" value="active">
                                                                    <button type="submit" class="dropdown-item text-success">
                                                                        <i class="fas fa-check me-2"></i>Approve
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($provider['status'] === 'active'): ?>
                                                            <li>
                                                                <form method="POST" style="display: inline;">
                                                                    <input type="hidden" name="action" value="update_status">
                                                                    <input type="hidden" name="provider_id" value="<?php echo $provider['id']; ?>">
                                                                    <input type="hidden" name="status" value="suspended">
                                                                    <button type="submit" class="dropdown-item text-danger">
                                                                        <i class="fas fa-ban me-2"></i>Suspend
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($provider['status'] === 'suspended'): ?>
                                                            <li>
                                                                <form method="POST" style="display: inline;">
                                                                    <input type="hidden" name="action" value="update_status">
                                                                    <input type="hidden" name="provider_id" value="<?php echo $provider['id']; ?>">
                                                                    <input type="hidden" name="status" value="active">
                                                                    <button type="submit" class="dropdown-item text-success">
                                                                        <i class="fas fa-check me-2"></i>Reactivate
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>
                                                        
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $provider['whatsapp_number']); ?>" 
                                                               target="_blank" class="dropdown-item">
                                                                <i class="fab fa-whatsapp me-2"></i>Contact via WhatsApp
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
        </div>
    </main>
</div>

<!-- Enhanced Mobile Styles for Provider Registrations -->
<style>
/* Mobile-optimized dropdown styles */
@media (max-width: 768px) {
    .btn-group .dropdown-toggle {
        min-height: 44px;
        padding: 12px 16px;
        font-size: 14px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .dropdown-menu {
        min-width: 200px;
        max-width: calc(100vw - 40px);
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border: 1px solid #e9ecef;
        margin-top: 4px;
    }

    .dropdown-item {
        padding: 12px 16px;
        font-size: 14px;
        min-height: 44px;
        display: flex;
        align-items: center;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        transition: all 0.2s ease;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background-color: #f8f9fa;
        transform: none;
    }

    .dropdown-item:active {
        background-color: #e9ecef;
        transform: scale(0.98);
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
    }

    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .table td {
        padding: 12px 8px;
        vertical-align: middle;
    }

    .btn-sm {
        padding: 8px 12px;
        font-size: 13px;
    }

    /* Loading state styles */
    .dropdown-item.loading {
        opacity: 0.7;
        pointer-events: none;
    }

    /* Touch feedback */
    .btn:active,
    .dropdown-toggle:active {
        transform: scale(0.95);
    }

    /* WhatsApp link styling */
    a[href*="wa.me"] {
        color: #25d366;
        transition: all 0.2s ease;
    }

    a[href*="wa.me"]:hover {
        color: #128c7e;
    }
}

/* Desktop improvements */
@media (min-width: 769px) {
    .dropdown-item {
        transition: all 0.2s ease;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        padding-left: 20px;
    }
}

/* Loading spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* Status badge improvements */
.badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
}

/* Card improvements */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.card-body {
    padding: 1.5rem;
}

/* Statistics cards mobile optimization */
@media (max-width: 768px) {
    .row.mb-4 .col-md-3 {
        margin-bottom: 1rem;
    }

    .card.bg-primary,
    .card.bg-warning,
    .card.bg-success,
    .card.bg-danger {
        border-radius: 8px;
    }

    .card-body h4 {
        font-size: 1.5rem;
    }
}
</style>

<!-- Enhanced Mobile Touch Handling for Provider Registrations -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced mobile dropdown handling
    function initializeMobileDropdowns() {
        const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');

        dropdownToggles.forEach(toggle => {
            // Remove any existing event listeners
            toggle.removeEventListener('touchend', handleDropdownTouch);
            toggle.removeEventListener('click', handleDropdownClick);

            // Add enhanced touch handling
            toggle.addEventListener('touchend', handleDropdownTouch, { passive: false });
            toggle.addEventListener('click', handleDropdownClick, { passive: false });

            // Add visual feedback
            toggle.addEventListener('touchstart', function(e) {
                this.style.transform = 'scale(0.95)';
                this.style.opacity = '0.8';
            }, { passive: true });

            toggle.addEventListener('touchend', function(e) {
                setTimeout(() => {
                    this.style.transform = '';
                    this.style.opacity = '';
                }, 150);
            }, { passive: true });
        });

        // Enhanced dropdown item handling
        const dropdownItems = document.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(item => {
            // Remove existing listeners
            item.removeEventListener('touchend', handleDropdownItemTouch);
            item.removeEventListener('click', handleDropdownItemClick);

            // Add enhanced touch handling
            item.addEventListener('touchend', handleDropdownItemTouch, { passive: false });
            item.addEventListener('click', handleDropdownItemClick, { passive: false });

            // Add visual feedback
            item.addEventListener('touchstart', function(e) {
                this.style.backgroundColor = '#f8f9fa';
                this.style.transform = 'scale(0.98)';
            }, { passive: true });

            item.addEventListener('touchend', function(e) {
                setTimeout(() => {
                    this.style.backgroundColor = '';
                    this.style.transform = '';
                }, 150);
            }, { passive: true });
        });
    }

    function handleDropdownTouch(e) {
        e.preventDefault();
        e.stopPropagation();

        const toggle = e.currentTarget;
        const dropdown = bootstrap.Dropdown.getOrCreateInstance(toggle);

        // Add a small delay to ensure touch is complete
        setTimeout(() => {
            dropdown.toggle();
        }, 100);
    }

    function handleDropdownClick(e) {
        // For desktop clicks, let Bootstrap handle it normally
        if (window.innerWidth > 768) {
            return;
        }

        // For mobile, prevent default and handle manually
        e.preventDefault();
        e.stopPropagation();

        const toggle = e.currentTarget;
        const dropdown = bootstrap.Dropdown.getOrCreateInstance(toggle);
        dropdown.toggle();
    }

    function handleDropdownItemTouch(e) {
        e.preventDefault();
        e.stopPropagation();

        const item = e.currentTarget;

        // Add loading state
        const originalContent = item.innerHTML;
        item.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

        setTimeout(() => {
            if (item.tagName === 'BUTTON' && item.type === 'submit') {
                // For form buttons, submit the form
                const form = item.closest('form');
                if (form) {
                    form.submit();
                }
            } else if (item.tagName === 'A' && item.href) {
                // For links, navigate
                if (item.target === '_blank') {
                    window.open(item.href, '_blank');
                } else {
                    window.location.href = item.href;
                }
            }
        }, 200);
    }

    function handleDropdownItemClick(e) {
        // For desktop clicks, let normal behavior handle it
        if (window.innerWidth > 768) {
            return;
        }

        // For mobile, prevent default and handle manually
        e.preventDefault();
        e.stopPropagation();

        handleDropdownItemTouch(e);
    }

    // Initialize on page load
    initializeMobileDropdowns();

    // Re-initialize when content changes (for dynamic content)
    const observer = new MutationObserver(function(mutations) {
        let shouldReinit = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && (
                        node.querySelector && (
                            node.querySelector('[data-bs-toggle="dropdown"]') ||
                            node.querySelector('.dropdown-item')
                        )
                    )) {
                        shouldReinit = true;
                    }
                });
            }
        });

        if (shouldReinit) {
            setTimeout(initializeMobileDropdowns, 100);
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Handle form submissions with loading states
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalContent = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                submitBtn.disabled = true;

                // Re-enable after 5 seconds in case of issues
                setTimeout(() => {
                    submitBtn.innerHTML = originalContent;
                    submitBtn.disabled = false;
                }, 5000);
            }
        });
    });

    // Enhanced WhatsApp link handling
    const whatsappLinks = document.querySelectorAll('a[href*="wa.me"]');
    whatsappLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add visual feedback
            this.style.opacity = '0.7';
            setTimeout(() => {
                this.style.opacity = '';
            }, 300);
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
