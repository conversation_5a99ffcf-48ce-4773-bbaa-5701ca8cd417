#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 241172480 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3600), pid=4140, tid=5512
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Jun  2 12:15:15 2025 Bangladesh Standard Time elapsed time: 56.951687 seconds (0d 0h 0m 56s)

---------------  T H R E A D  ---------------

Current thread (0x0000028a525b52b0):  VMThread "VM Thread"          [id=5512, stack(0x000000f404a00000,0x000000f404b00000) (1024K)]

Stack: [0x000000f404a00000,0x000000f404b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0x6c74d5]
V  [jvm.dll+0x6bbeca]
V  [jvm.dll+0x355bca]
V  [jvm.dll+0x35d816]
V  [jvm.dll+0x3ae67e]
V  [jvm.dll+0x3ae928]
V  [jvm.dll+0x3295dc]
V  [jvm.dll+0x329667]
V  [jvm.dll+0x36df1c]
V  [jvm.dll+0x36c71d]
V  [jvm.dll+0x328d51]
V  [jvm.dll+0x36be2a]
V  [jvm.dll+0x85ef18]
V  [jvm.dll+0x860014]
V  [jvm.dll+0x860550]
V  [jvm.dll+0x8607f3]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x000000f40e9fc030): G1CollectForAllocation, mode: safepoint, requested by thread 0x0000028aa742a410


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000028a9cef5240, length=163, elements={
0x0000028a33bf63c0, 0x0000028a525cedc0, 0x0000028a525d0480, 0x0000028a525d47d0,
0x0000028a525d6c60, 0x0000028a525d9e70, 0x0000028a525e0910, 0x0000028a52692570,
0x0000028a5269ac30, 0x0000028a525e0fa0, 0x0000028a525ddb20, 0x0000028a525df560,
0x0000028a525de840, 0x0000028a525e0280, 0x0000028a525dfbf0, 0x0000028a9981faf0,
0x0000028a99822f70, 0x0000028a99824320, 0x0000028a99820180, 0x0000028a99820ea0,
0x0000028a9981edd0, 0x0000028a99823c90, 0x0000028a9c3e14f0, 0x0000028a9c3e1b80,
0x0000028a9c3df420, 0x0000028a9ac8d610, 0x0000028a9ac8dca0, 0x0000028a9ac8c8f0,
0x0000028a9b40c090, 0x0000028a9b407ef0, 0x0000028a9ce075c0, 0x0000028a9ce0a3b0,
0x0000028aa477dcb0, 0x0000028aa477c900, 0x0000028aa4776d20, 0x0000028aa4778df0,
0x0000028aa47773b0, 0x0000028aa536ad90, 0x0000028aa536f5c0, 0x0000028aa536fc50,
0x0000028aa536d4f0, 0x0000028aa5370970, 0x0000028aa536a700, 0x0000028aa536b420,
0x0000028aa53699e0, 0x0000028aa536c140, 0x0000028a9b826a90, 0x0000028aa536bab0,
0x0000028aa536c7d0, 0x0000028a9b822670, 0x0000028aa536ce60, 0x0000028aa536db80,
0x0000028aa5376be0, 0x0000028aa5377270, 0x0000028aa5377900, 0x0000028aa5377f90,
0x0000028aa5378620, 0x0000028aa5378cb0, 0x0000028aa4ba4a90, 0x0000028aa4ba08f0,
0x0000028aa4b9f540, 0x0000028aa4ba2330, 0x0000028aa4ba5120, 0x0000028aa4b9fbd0,
0x0000028aa4ba3050, 0x0000028aa4ba0260, 0x0000028aa4ba36e0, 0x0000028aa4b9e820,
0x0000028aa4b9e190, 0x0000028aa4ba3d70, 0x0000028aa4ba1610, 0x0000028aa4b9eeb0,
0x0000028aa4ba0f80, 0x0000028aa4ba4400, 0x0000028aa4ba57b0, 0x0000028a9ac90a90,
0x0000028a9ac8e9c0, 0x0000028a9ac8f050, 0x0000028a9ac8a820, 0x0000028a9ac8f6e0,
0x0000028a9ac90400, 0x0000028a9ac917b0, 0x0000028a9ac91e40, 0x0000028a9ac8c260,
0x0000028a9ac8aeb0, 0x0000028a9ac8b540, 0x0000028a9ac8cf80, 0x0000028aa5345f20,
0x0000028aa53472d0, 0x0000028aa53437c0, 0x0000028aa5341d80, 0x0000028aa53465b0,
0x0000028aa5346c40, 0x0000028aa53444e0, 0x0000028aa5343130, 0x0000028aa5347960,
0x0000028aa5347ff0, 0x0000028aa5341060, 0x0000028aa5348680, 0x0000028aa53416f0,
0x0000028aa534fca0, 0x0000028aa534ceb0, 0x0000028aa5348d10, 0x0000028aa534d540,
0x0000028aa534a750, 0x0000028aa5350330, 0x0000028aa53493a0, 0x0000028aa534e8f0,
0x0000028aa534c190, 0x0000028aa534ef80, 0x0000028aa534f610, 0x0000028aa5349a30,
0x0000028aa534ade0, 0x0000028aa534dbd0, 0x0000028aa534b470, 0x0000028aa534bb00,
0x0000028aa534e260, 0x0000028aa536ef30, 0x0000028aa536e210, 0x0000028aa742aaa0,
0x0000028aa7423b10, 0x0000028aa7426900, 0x0000028aa742b130, 0x0000028aa7427620,
0x0000028aa7427cb0, 0x0000028aa74241a0, 0x0000028aa74289d0, 0x0000028aa742be50,
0x0000028aa742b7c0, 0x0000028aa742c4e0, 0x0000028aa742cb70, 0x0000028aa742d200,
0x0000028aa742d890, 0x0000028aa742df20, 0x0000028aa742f2d0, 0x0000028aa742e5b0,
0x0000028aa742ec40, 0x0000028aa7428340, 0x0000028aa742f960, 0x0000028aa742fff0,
0x0000028aa7430680, 0x0000028aa7430d10, 0x0000028aa74313a0, 0x0000028aa74320c0,
0x0000028aa7431a30, 0x0000028aa5a99a60, 0x0000028aa5a9c1c0, 0x0000028aa5a96c70,
0x0000028aa5a958c0, 0x0000028aa7425550, 0x0000028aa742a410, 0x0000028aa7423480,
0x0000028aa7425be0, 0x0000028a9c3de700, 0x0000028a9c3e28a0, 0x0000028aa4778760,
0x0000028aa477cf90, 0x0000028aa477bbe0, 0x0000028aa4779480, 0x0000028aa477d620,
0x0000028aa4777a40, 0x0000028aa4776690, 0x0000028aa477a830
}

Java Threads: ( => current thread )
  0x0000028a33bf63c0 JavaThread "main"                              [_thread_blocked, id=5212, stack(0x000000f404300000,0x000000f404400000) (1024K)]
  0x0000028a525cedc0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=2856, stack(0x000000f404b00000,0x000000f404c00000) (1024K)]
  0x0000028a525d0480 JavaThread "Finalizer"                  daemon [_thread_blocked, id=10912, stack(0x000000f404c00000,0x000000f404d00000) (1024K)]
  0x0000028a525d47d0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=18680, stack(0x000000f404d00000,0x000000f404e00000) (1024K)]
  0x0000028a525d6c60 JavaThread "Attach Listener"            daemon [_thread_blocked, id=15572, stack(0x000000f404e00000,0x000000f404f00000) (1024K)]
  0x0000028a525d9e70 JavaThread "Service Thread"             daemon [_thread_blocked, id=15688, stack(0x000000f404f00000,0x000000f405000000) (1024K)]
  0x0000028a525e0910 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=11284, stack(0x000000f405000000,0x000000f405100000) (1024K)]
  0x0000028a52692570 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=7204, stack(0x000000f405100000,0x000000f405200000) (1024K)]
  0x0000028a5269ac30 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=16360, stack(0x000000f405200000,0x000000f405300000) (1024K)]
  0x0000028a525e0fa0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=17776, stack(0x000000f405300000,0x000000f405400000) (1024K)]
  0x0000028a525ddb20 JavaThread "Notification Thread"        daemon [_thread_blocked, id=17572, stack(0x000000f405400000,0x000000f405500000) (1024K)]
  0x0000028a525df560 JavaThread "Daemon health stats"               [_thread_blocked, id=18416, stack(0x000000f405600000,0x000000f405700000) (1024K)]
  0x0000028a525de840 JavaThread "Incoming local TCP Connector on port 50737"        [_thread_in_native, id=3272, stack(0x000000f405e00000,0x000000f405f00000) (1024K)]
  0x0000028a525e0280 JavaThread "Daemon periodic checks"            [_thread_blocked, id=11900, stack(0x000000f405f00000,0x000000f406000000) (1024K)]
  0x0000028a525dfbf0 JavaThread "Daemon"                            [_thread_blocked, id=17444, stack(0x000000f406000000,0x000000f406100000) (1024K)]
  0x0000028a9981faf0 JavaThread "Daemon worker"                     [_thread_blocked, id=18248, stack(0x000000f406300000,0x000000f406400000) (1024K)]
  0x0000028a99822f70 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=16056, stack(0x000000f406700000,0x000000f406800000) (1024K)]
  0x0000028a99824320 JavaThread "File lock request listener"        [_thread_in_native, id=7844, stack(0x000000f406800000,0x000000f406900000) (1024K)]
  0x0000028a99820180 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)"        [_thread_blocked, id=7620, stack(0x000000f406900000,0x000000f406a00000) (1024K)]
  0x0000028a99820ea0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileContent)"        [_thread_blocked, id=4648, stack(0x000000f406a00000,0x000000f406b00000) (1024K)]
  0x0000028a9981edd0 JavaThread "File watcher server"        daemon [_thread_blocked, id=3368, stack(0x000000f406f00000,0x000000f407000000) (1024K)]
  0x0000028a99823c90 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=2608, stack(0x000000f407000000,0x000000f407100000) (1024K)]
  0x0000028a9c3e14f0 JavaThread "jar transforms"                    [_thread_blocked, id=10336, stack(0x000000f407400000,0x000000f407500000) (1024K)]
  0x0000028a9c3e1b80 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=12480, stack(0x000000f406e00000,0x000000f406f00000) (1024K)]
  0x0000028a9c3df420 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=10516, stack(0x000000f407600000,0x000000f407700000) (1024K)]
  0x0000028a9ac8d610 JavaThread "File lock release action executor"        [_thread_blocked, id=18720, stack(0x000000f408c00000,0x000000f408d00000) (1024K)]
  0x0000028a9ac8dca0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=9300, stack(0x000000f408d00000,0x000000f408e00000) (1024K)]
  0x0000028a9ac8c8f0 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=1920, stack(0x000000f408e00000,0x000000f408f00000) (1024K)]
  0x0000028a9b40c090 JavaThread "Memory manager"                    [_thread_blocked, id=8908, stack(0x000000f40a300000,0x000000f40a400000) (1024K)]
  0x0000028a9b407ef0 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=16216, stack(0x000000f405500000,0x000000f405600000) (1024K)]
  0x0000028a9ce075c0 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=9676, stack(0x000000f40a600000,0x000000f40a700000) (1024K)]
  0x0000028a9ce0a3b0 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=16072, stack(0x000000f40a700000,0x000000f40a800000) (1024K)]
  0x0000028aa477dcb0 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=12804, stack(0x000000f40aa00000,0x000000f40ab00000) (1024K)]
  0x0000028aa477c900 JavaThread "Exec process"                      [_thread_blocked, id=9964, stack(0x000000f40a800000,0x000000f40a900000) (1024K)]
  0x0000028aa4776d20 JavaThread "Exec process Thread 2"             [_thread_blocked, id=17076, stack(0x000000f40a900000,0x000000f40aa00000) (1024K)]
  0x0000028aa4778df0 JavaThread "Exec process Thread 3"             [_thread_blocked, id=224, stack(0x000000f40ac00000,0x000000f40ad00000) (1024K)]
  0x0000028aa47773b0 JavaThread "included builds"                   [_thread_blocked, id=16080, stack(0x000000f40b000000,0x000000f40b100000) (1024K)]
  0x0000028aa536ad90 JavaThread "VFS cleanup"                       [_thread_blocked, id=5840, stack(0x000000f406400000,0x000000f406500000) (1024K)]
  0x0000028aa536f5c0 JavaThread "Handler for socket connection from /127.0.0.1:50737 to /127.0.0.1:50802"        [_thread_in_native, id=756, stack(0x000000f406100000,0x000000f406200000) (1024K)]
  0x0000028aa536fc50 JavaThread "Cancel handler"                    [_thread_blocked, id=2528, stack(0x000000f406200000,0x000000f406300000) (1024K)]
  0x0000028aa536d4f0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:50737 to /127.0.0.1:50802"        [_thread_blocked, id=1368, stack(0x000000f406500000,0x000000f406600000) (1024K)]
  0x0000028aa5370970 JavaThread "Stdin handler"                     [_thread_blocked, id=16300, stack(0x000000f406600000,0x000000f406700000) (1024K)]
  0x0000028aa536a700 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=19324, stack(0x000000f406c00000,0x000000f406d00000) (1024K)]
  0x0000028aa536b420 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\.gradle\8.11.1\fileHashes)"        [_thread_blocked, id=8760, stack(0x000000f406d00000,0x000000f406e00000) (1024K)]
  0x0000028aa53699e0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\.gradle\buildOutputCleanup)"        [_thread_blocked, id=11452, stack(0x000000f407100000,0x000000f407200000) (1024K)]
  0x0000028aa536c140 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\.gradle\8.11.1\checksums)"        [_thread_blocked, id=19312, stack(0x000000f407200000,0x000000f407300000) (1024K)]
  0x0000028a9b826a90 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=13524, stack(0x000000f407300000,0x000000f407400000) (1024K)]
  0x0000028aa536bab0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.11.1\md-rule)"        [_thread_blocked, id=10232, stack(0x000000f407500000,0x000000f407600000) (1024K)]
  0x0000028aa536c7d0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.11.1\md-supplier)"        [_thread_blocked, id=1812, stack(0x000000f407700000,0x000000f407800000) (1024K)]
  0x0000028a9b822670 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=17808, stack(0x000000f407800000,0x000000f407900000) (1024K)]
  0x0000028aa536ce60 JavaThread "Unconstrained build operations"        [_thread_blocked, id=8060, stack(0x000000f407900000,0x000000f407a00000) (1024K)]
  0x0000028aa536db80 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=19164, stack(0x000000f407a00000,0x000000f407b00000) (1024K)]
  0x0000028aa5376be0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=13028, stack(0x000000f407b00000,0x000000f407c00000) (1024K)]
  0x0000028aa5377270 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=19172, stack(0x000000f407c00000,0x000000f407d00000) (1024K)]
  0x0000028aa5377900 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=13300, stack(0x000000f407d00000,0x000000f407e00000) (1024K)]
  0x0000028aa5377f90 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=17124, stack(0x000000f407e00000,0x000000f407f00000) (1024K)]
  0x0000028aa5378620 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=10108, stack(0x000000f407f00000,0x000000f408000000) (1024K)]
  0x0000028aa5378cb0 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=18692, stack(0x000000f408000000,0x000000f408100000) (1024K)]
  0x0000028aa4ba4a90 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=14256, stack(0x000000f408100000,0x000000f408200000) (1024K)]
  0x0000028aa4ba08f0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=12596, stack(0x000000f408200000,0x000000f408300000) (1024K)]
  0x0000028aa4b9f540 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=2096, stack(0x000000f408300000,0x000000f408400000) (1024K)]
  0x0000028aa4ba2330 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=1888, stack(0x000000f408400000,0x000000f408500000) (1024K)]
  0x0000028aa4ba5120 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=10128, stack(0x000000f408500000,0x000000f408600000) (1024K)]
  0x0000028aa4b9fbd0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=9960, stack(0x000000f408600000,0x000000f408700000) (1024K)]
  0x0000028aa4ba3050 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=10896, stack(0x000000f408700000,0x000000f408800000) (1024K)]
  0x0000028aa4ba0260 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=18028, stack(0x000000f408800000,0x000000f408900000) (1024K)]
  0x0000028aa4ba36e0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=12360, stack(0x000000f408900000,0x000000f408a00000) (1024K)]
  0x0000028aa4b9e820 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=11484, stack(0x000000f408a00000,0x000000f408b00000) (1024K)]
  0x0000028aa4b9e190 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=19376, stack(0x000000f408b00000,0x000000f408c00000) (1024K)]
  0x0000028aa4ba3d70 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=9728, stack(0x000000f408f00000,0x000000f409000000) (1024K)]
  0x0000028aa4ba1610 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=12716, stack(0x000000f409000000,0x000000f409100000) (1024K)]
  0x0000028aa4b9eeb0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=2632, stack(0x000000f409100000,0x000000f409200000) (1024K)]
  0x0000028aa4ba0f80 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=7808, stack(0x000000f409200000,0x000000f409300000) (1024K)]
  0x0000028aa4ba4400 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=18000, stack(0x000000f409300000,0x000000f409400000) (1024K)]
  0x0000028aa4ba57b0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=16060, stack(0x000000f409400000,0x000000f409500000) (1024K)]
  0x0000028a9ac90a90 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=10216, stack(0x000000f409500000,0x000000f409600000) (1024K)]
  0x0000028a9ac8e9c0 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=2500, stack(0x000000f409600000,0x000000f409700000) (1024K)]
  0x0000028a9ac8f050 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=7184, stack(0x000000f409700000,0x000000f409800000) (1024K)]
  0x0000028a9ac8a820 JavaThread "build event listener"              [_thread_blocked, id=14152, stack(0x000000f409800000,0x000000f409900000) (1024K)]
  0x0000028a9ac8f6e0 JavaThread "build event listener"              [_thread_blocked, id=18492, stack(0x000000f409900000,0x000000f409a00000) (1024K)]
  0x0000028a9ac90400 JavaThread "pool-7-thread-1"                   [_thread_blocked, id=17116, stack(0x000000f409a00000,0x000000f409b00000) (1024K)]
  0x0000028a9ac917b0 JavaThread "build event listener"              [_thread_blocked, id=9920, stack(0x000000f409b00000,0x000000f409c00000) (1024K)]
  0x0000028a9ac91e40 JavaThread "SentryExecutorServiceThreadFactory-0" daemon [_thread_blocked, id=7356, stack(0x000000f409c00000,0x000000f409d00000) (1024K)]
  0x0000028a9ac8c260 JavaThread "SentryAsyncConnection-0"    daemon [_thread_blocked, id=12124, stack(0x000000f409d00000,0x000000f409e00000) (1024K)]
  0x0000028a9ac8aeb0 JavaThread "build event listener"              [_thread_blocked, id=7436, stack(0x000000f409e00000,0x000000f409f00000) (1024K)]
  0x0000028a9ac8b540 JavaThread "included builds"                   [_thread_blocked, id=8140, stack(0x000000f409f00000,0x000000f40a000000) (1024K)]
  0x0000028a9ac8cf80 JavaThread "Execution worker"                  [_thread_blocked, id=9976, stack(0x000000f40a000000,0x000000f40a100000) (1024K)]
  0x0000028aa5345f20 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=3248, stack(0x000000f40a100000,0x000000f40a200000) (1024K)]
  0x0000028aa53472d0 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=2068, stack(0x000000f40a200000,0x000000f40a300000) (1024K)]
  0x0000028aa53437c0 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=13264, stack(0x000000f40a400000,0x000000f40a500000) (1024K)]
  0x0000028aa5341d80 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=13736, stack(0x000000f40a500000,0x000000f40a600000) (1024K)]
  0x0000028aa53465b0 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=17128, stack(0x000000f40ab00000,0x000000f40ac00000) (1024K)]
  0x0000028aa5346c40 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=3236, stack(0x000000f40ad00000,0x000000f40ae00000) (1024K)]
  0x0000028aa53444e0 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=12044, stack(0x000000f40ae00000,0x000000f40af00000) (1024K)]
  0x0000028aa5343130 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=15924, stack(0x000000f40af00000,0x000000f40b000000) (1024K)]
  0x0000028aa5347960 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=17660, stack(0x000000f40b100000,0x000000f40b200000) (1024K)]
  0x0000028aa5347ff0 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=11148, stack(0x000000f40b200000,0x000000f40b300000) (1024K)]
  0x0000028aa5341060 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=16324, stack(0x000000f40b300000,0x000000f40b400000) (1024K)]
  0x0000028aa5348680 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=4620, stack(0x000000f40b400000,0x000000f40b500000) (1024K)]
  0x0000028aa53416f0 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=17868, stack(0x000000f40b500000,0x000000f40b600000) (1024K)]
  0x0000028aa534fca0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=15864, stack(0x000000f40b600000,0x000000f40b700000) (1024K)]
  0x0000028aa534ceb0 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=7940, stack(0x000000f40b700000,0x000000f40b800000) (1024K)]
  0x0000028aa5348d10 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=9136, stack(0x000000f40b800000,0x000000f40b900000) (1024K)]
  0x0000028aa534d540 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=9032, stack(0x000000f40b900000,0x000000f40ba00000) (1024K)]
  0x0000028aa534a750 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=15980, stack(0x000000f40ba00000,0x000000f40bb00000) (1024K)]
  0x0000028aa5350330 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=7624, stack(0x000000f40bc00000,0x000000f40bd00000) (1024K)]
  0x0000028aa53493a0 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=12100, stack(0x000000f40bd00000,0x000000f40be00000) (1024K)]
  0x0000028aa534e8f0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=19416, stack(0x000000f40be00000,0x000000f40bf00000) (1024K)]
  0x0000028aa534c190 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=15512, stack(0x000000f40bf00000,0x000000f40c000000) (1024K)]
  0x0000028aa534ef80 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=14116, stack(0x000000f40c000000,0x000000f40c100000) (1024K)]
  0x0000028aa534f610 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=18292, stack(0x000000f40c100000,0x000000f40c200000) (1024K)]
  0x0000028aa5349a30 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\.gradle\8.11.1\executionHistory)"        [_thread_blocked, id=4952, stack(0x000000f40c200000,0x000000f40c300000) (1024K)]
  0x0000028aa534ade0 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=19368, stack(0x000000f40c300000,0x000000f40c400000) (1024K)]
  0x0000028aa534dbd0 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=3588, stack(0x000000f40c400000,0x000000f40c500000) (1024K)]
  0x0000028aa534b470 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=18724, stack(0x000000f40c500000,0x000000f40c600000) (1024K)]
  0x0000028aa534bb00 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=16368, stack(0x000000f40c600000,0x000000f40c700000) (1024K)]
  0x0000028aa534e260 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=15836, stack(0x000000f40c700000,0x000000f40c800000) (1024K)]
  0x0000028aa536ef30 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=18284, stack(0x000000f40c800000,0x000000f40c900000) (1024K)]
  0x0000028aa536e210 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=7676, stack(0x000000f40c900000,0x000000f40ca00000) (1024K)]
  0x0000028aa742aaa0 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=9864, stack(0x000000f40ca00000,0x000000f40cb00000) (1024K)]
  0x0000028aa7423b10 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=19216, stack(0x000000f40cb00000,0x000000f40cc00000) (1024K)]
  0x0000028aa7426900 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=17720, stack(0x000000f40cc00000,0x000000f40cd00000) (1024K)]
  0x0000028aa742b130 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=17556, stack(0x000000f40cd00000,0x000000f40ce00000) (1024K)]
  0x0000028aa7427620 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=18628, stack(0x000000f40ce00000,0x000000f40cf00000) (1024K)]
  0x0000028aa7427cb0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=15696, stack(0x000000f40cf00000,0x000000f40d000000) (1024K)]
  0x0000028aa74241a0 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=8044, stack(0x000000f40d000000,0x000000f40d100000) (1024K)]
  0x0000028aa74289d0 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=17480, stack(0x000000f40d100000,0x000000f40d200000) (1024K)]
  0x0000028aa742be50 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=12228, stack(0x000000f40d200000,0x000000f40d300000) (1024K)]
  0x0000028aa742b7c0 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=17584, stack(0x000000f40d300000,0x000000f40d400000) (1024K)]
  0x0000028aa742c4e0 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=2972, stack(0x000000f40d400000,0x000000f40d500000) (1024K)]
  0x0000028aa742cb70 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=14156, stack(0x000000f40d500000,0x000000f40d600000) (1024K)]
  0x0000028aa742d200 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=5956, stack(0x000000f40d600000,0x000000f40d700000) (1024K)]
  0x0000028aa742d890 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=16052, stack(0x000000f40d700000,0x000000f40d800000) (1024K)]
  0x0000028aa742df20 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=18172, stack(0x000000f40d800000,0x000000f40d900000) (1024K)]
  0x0000028aa742f2d0 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=12472, stack(0x000000f40d900000,0x000000f40da00000) (1024K)]
  0x0000028aa742e5b0 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=3932, stack(0x000000f40da00000,0x000000f40db00000) (1024K)]
  0x0000028aa742ec40 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=13344, stack(0x000000f40db00000,0x000000f40dc00000) (1024K)]
  0x0000028aa7428340 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=16776, stack(0x000000f40dc00000,0x000000f40dd00000) (1024K)]
  0x0000028aa742f960 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=4516, stack(0x000000f40dd00000,0x000000f40de00000) (1024K)]
  0x0000028aa742fff0 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=4844, stack(0x000000f40de00000,0x000000f40df00000) (1024K)]
  0x0000028aa7430680 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=19116, stack(0x000000f40df00000,0x000000f40e000000) (1024K)]
  0x0000028aa7430d10 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=13660, stack(0x000000f40e000000,0x000000f40e100000) (1024K)]
  0x0000028aa74313a0 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=16176, stack(0x000000f40e100000,0x000000f40e200000) (1024K)]
  0x0000028aa74320c0 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=9924, stack(0x000000f40e200000,0x000000f40e300000) (1024K)]
  0x0000028aa7431a30 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=18656, stack(0x000000f40e300000,0x000000f40e400000) (1024K)]
  0x0000028aa5a99a60 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=2292, stack(0x000000f40e400000,0x000000f40e500000) (1024K)]
  0x0000028aa5a9c1c0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=17540, stack(0x000000f40e500000,0x000000f40e600000) (1024K)]
  0x0000028aa5a96c70 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=19104, stack(0x000000f40e600000,0x000000f40e700000) (1024K)]
  0x0000028aa5a958c0 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=9416, stack(0x000000f40e700000,0x000000f40e800000) (1024K)]
  0x0000028aa7425550 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=14736, stack(0x000000f40e800000,0x000000f40e900000) (1024K)]
  0x0000028aa742a410 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=4636, stack(0x000000f40e900000,0x000000f40ea00000) (1024K)]
  0x0000028aa7423480 JavaThread "WorkerExecutor Queue Thread 6"        [_thread_blocked, id=19036, stack(0x000000f40ea00000,0x000000f40eb00000) (1024K)]
  0x0000028aa7425be0 JavaThread "WorkerExecutor Queue Thread 7"        [_thread_blocked, id=16316, stack(0x000000f40eb00000,0x000000f40ec00000) (1024K)]
  0x0000028a9c3de700 JavaThread "WorkerExecutor Queue Thread 8"        [_thread_blocked, id=19428, stack(0x000000f40ec00000,0x000000f40ed00000) (1024K)]
  0x0000028a9c3e28a0 JavaThread "WorkerExecutor Queue Thread 9"        [_thread_blocked, id=10172, stack(0x000000f40ed00000,0x000000f40ee00000) (1024K)]
  0x0000028aa4778760 JavaThread "ForkJoinPool-1-worker-1"    daemon [_thread_blocked, id=7548, stack(0x000000f40ee00000,0x000000f40ef00000) (1024K)]
  0x0000028aa477cf90 JavaThread "ForkJoinPool-1-worker-2"    daemon [_thread_blocked, id=11444, stack(0x000000f40ef00000,0x000000f40f000000) (1024K)]
  0x0000028aa477bbe0 JavaThread "ForkJoinPool-1-worker-3"    daemon [_thread_blocked, id=10588, stack(0x000000f40f000000,0x000000f40f100000) (1024K)]
  0x0000028aa4779480 JavaThread "ForkJoinPool-1-worker-4"    daemon [_thread_blocked, id=992, stack(0x000000f40f100000,0x000000f40f200000) (1024K)]
  0x0000028aa477d620 JavaThread "ForkJoinPool-1-worker-5"    daemon [_thread_blocked, id=9736, stack(0x000000f40f200000,0x000000f40f300000) (1024K)]
  0x0000028aa4777a40 JavaThread "ForkJoinPool-1-worker-6"    daemon [_thread_blocked, id=12020, stack(0x000000f40f300000,0x000000f40f400000) (1024K)]
  0x0000028aa4776690 JavaThread "ForkJoinPool-1-worker-7"    daemon [_thread_blocked, id=3604, stack(0x000000f40f400000,0x000000f40f500000) (1024K)]
  0x0000028aa477a830 JavaThread "ForkJoinPool-1-worker-8"    daemon [_thread_blocked, id=11596, stack(0x000000f40f500000,0x000000f40f600000) (1024K)]
Total: 163

Other Threads:
=>0x0000028a525b52b0 VMThread "VM Thread"                           [id=5512, stack(0x000000f404a00000,0x000000f404b00000) (1024K)]
  0x0000028a52670c30 WatcherThread "VM Periodic Task Thread"        [id=10652, stack(0x000000f404900000,0x000000f404a00000) (1024K)]
  0x0000028a33c4fc30 WorkerThread "GC Thread#0"                     [id=12396, stack(0x000000f404400000,0x000000f404500000) (1024K)]
  0x0000028a9807b910 WorkerThread "GC Thread#1"                     [id=7964, stack(0x000000f405700000,0x000000f405800000) (1024K)]
  0x0000028a981147b0 WorkerThread "GC Thread#2"                     [id=13412, stack(0x000000f405800000,0x000000f405900000) (1024K)]
  0x0000028a98114b50 WorkerThread "GC Thread#3"                     [id=10456, stack(0x000000f405900000,0x000000f405a00000) (1024K)]
  0x0000028a982d0f90 WorkerThread "GC Thread#4"                     [id=17196, stack(0x000000f405a00000,0x000000f405b00000) (1024K)]
  0x0000028a982d1330 WorkerThread "GC Thread#5"                     [id=18188, stack(0x000000f405b00000,0x000000f405c00000) (1024K)]
  0x0000028a983b35d0 WorkerThread "GC Thread#6"                     [id=4684, stack(0x000000f405c00000,0x000000f405d00000) (1024K)]
  0x0000028a982d16d0 WorkerThread "GC Thread#7"                     [id=3616, stack(0x000000f405d00000,0x000000f405e00000) (1024K)]
  0x0000028a33c60cd0 ConcurrentGCThread "G1 Main Marker"            [id=1344, stack(0x000000f404500000,0x000000f404600000) (1024K)]
  0x0000028a33c61e80 WorkerThread "G1 Conc#0"                       [id=13684, stack(0x000000f404600000,0x000000f404700000) (1024K)]
  0x0000028a99a925d0 WorkerThread "G1 Conc#1"                       [id=12624, stack(0x000000f406b00000,0x000000f406c00000) (1024K)]
  0x0000028a524f0a80 ConcurrentGCThread "G1 Refine#0"               [id=16148, stack(0x000000f404700000,0x000000f404800000) (1024K)]
  0x0000028a524f1500 ConcurrentGCThread "G1 Service"                [id=15872, stack(0x000000f404800000,0x000000f404900000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0    56993 22126 %     4       com.android.tools.r8.dex.C::a @ 143 (831 bytes)
C1 CompilerThread0    56993 22785       2       com.android.tools.r8.internal.BE::a (851 bytes)
C2 CompilerThread1    56993 22388       4       com.android.tools.r8.dex.C::b (267 bytes)
C2 CompilerThread2    56993 22182 %     4       com.android.tools.r8.dex.C::a @ 298 (1021 bytes)
Total: 4

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fff7e33cd68] Threads_lock - owner thread: 0x0000028a525b52b0
[0x00007fff7e33ce68] Heap_lock - owner thread: 0x0000028aa742a410

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000028a53000000-0x0000028a53c90000-0x0000028a53c90000), size 13172736, SharedBaseAddress: 0x0000028a53000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000028a54000000-0x0000028a94000000, reserved size: 1073741824
Narrow klass base: 0x0000028a53000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 546816K, used 400896K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 146264K, committed 148800K, reserved 1179648K
  class space    used 20101K, committed 21376K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000| PB 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080200000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080800000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%|HS|  |TAMS 0x0000000082d00000| PB 0x0000000082c00000| Complete 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%|HS|  |TAMS 0x0000000082e00000| PB 0x0000000082d00000| Complete 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HS|  |TAMS 0x0000000082f00000| PB 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%|HS|  |TAMS 0x0000000083000000| PB 0x0000000082f00000| Complete 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HC|  |TAMS 0x0000000083100000| PB 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HS|  |TAMS 0x0000000083200000| PB 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HC|  |TAMS 0x0000000083300000| PB 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083500000| PB 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HC|  |TAMS 0x0000000083600000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HC|  |TAMS 0x0000000083700000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%|HS|  |TAMS 0x0000000083f00000| PB 0x0000000083e00000| Complete 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%|HS|  |TAMS 0x0000000084000000| PB 0x0000000083f00000| Complete 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%|HS|  |TAMS 0x0000000084100000| PB 0x0000000084000000| Complete 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%|HS|  |TAMS 0x0000000084a00000| PB 0x0000000084900000| Complete 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%|HS|  |TAMS 0x0000000084e00000| PB 0x0000000084d00000| Complete 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%|HC|  |TAMS 0x0000000084f00000| PB 0x0000000084e00000| Complete 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%|HC|  |TAMS 0x0000000085000000| PB 0x0000000084f00000| Complete 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%|HS|  |TAMS 0x0000000086300000| PB 0x0000000086200000| Complete 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%|HS|  |TAMS 0x0000000086500000| PB 0x0000000086400000| Complete 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%|HC|  |TAMS 0x0000000086600000| PB 0x0000000086500000| Complete 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%|HS|  |TAMS 0x0000000086800000| PB 0x0000000086700000| Complete 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%|HS|  |TAMS 0x0000000086900000| PB 0x0000000086800000| Complete 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%|HC|  |TAMS 0x0000000086a00000| PB 0x0000000086900000| Complete 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%|HS|  |TAMS 0x0000000086b00000| PB 0x0000000086a00000| Complete 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%|HS|  |TAMS 0x0000000086d00000| PB 0x0000000086c00000| Complete 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%|HS|  |TAMS 0x0000000086e00000| PB 0x0000000086d00000| Complete 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%|HS|  |TAMS 0x0000000086f00000| PB 0x0000000086e00000| Complete 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%|HS|  |TAMS 0x0000000087000000| PB 0x0000000086f00000| Complete 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%|HS|  |TAMS 0x0000000087500000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%|HC|  |TAMS 0x0000000087600000| PB 0x0000000087500000| Complete 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%|HS|  |TAMS 0x0000000087700000| PB 0x0000000087600000| Complete 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%|HS|  |TAMS 0x0000000087900000| PB 0x0000000087800000| Complete 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%|HS|  |TAMS 0x0000000087c00000| PB 0x0000000087b00000| Complete 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%|HC|  |TAMS 0x0000000087d00000| PB 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%|HS|  |TAMS 0x0000000088100000| PB 0x0000000088000000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%|HC|  |TAMS 0x0000000088200000| PB 0x0000000088100000| Complete 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088400000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088500000| PB 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088700000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%|HS|  |TAMS 0x0000000088c00000| PB 0x0000000088b00000| Complete 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%|HC|  |TAMS 0x0000000088d00000| PB 0x0000000088c00000| Complete 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%|HC|  |TAMS 0x0000000088e00000| PB 0x0000000088d00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%|HC|  |TAMS 0x0000000088f00000| PB 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000089000000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%|HS|  |TAMS 0x0000000089a00000| PB 0x0000000089900000| Complete 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x000000008a000000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a300000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a400000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a900000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ad00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ae00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008af00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008b000000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b100000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b200000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b300000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| O|  |TAMS 0x000000008b400000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b500000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b600000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b700000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| O|  |TAMS 0x000000008b800000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b900000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%|HS|  |TAMS 0x000000008ba00000| PB 0x000000008b900000| Complete 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%|HC|  |TAMS 0x000000008bb00000| PB 0x000000008ba00000| Complete 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%|HC|  |TAMS 0x000000008bc00000| PB 0x000000008bb00000| Complete 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%|HS|  |TAMS 0x000000008bd00000| PB 0x000000008bc00000| Complete 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%|HC|  |TAMS 0x000000008be00000| PB 0x000000008bd00000| Complete 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%|HC|  |TAMS 0x000000008bf00000| PB 0x000000008be00000| Complete 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008c000000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%|HS|  |TAMS 0x000000008c100000| PB 0x000000008c000000| Complete 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%|HS|  |TAMS 0x000000008c200000| PB 0x000000008c100000| Complete 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%|HC|  |TAMS 0x000000008c300000| PB 0x000000008c200000| Complete 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%|HS|  |TAMS 0x000000008c400000| PB 0x000000008c300000| Complete 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%|HS|  |TAMS 0x000000008c500000| PB 0x000000008c400000| Complete 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%|HS|  |TAMS 0x000000008c600000| PB 0x000000008c500000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c700000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c800000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| O|  |TAMS 0x000000008c900000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| O|  |TAMS 0x000000008ca00000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| O|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%|HS|  |TAMS 0x000000008cc00000| PB 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| O|  |TAMS 0x000000008cd00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| O|  |TAMS 0x000000008ce00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008cf00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%|HS|  |TAMS 0x000000008d000000| PB 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%|HC|  |TAMS 0x000000008d100000| PB 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%|HC|  |TAMS 0x000000008d200000| PB 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%|HC|  |TAMS 0x000000008d300000| PB 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%|HS|  |TAMS 0x000000008d400000| PB 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d500000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d600000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d700000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| O|  |TAMS 0x000000008d800000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d900000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| O|  |TAMS 0x000000008da00000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008db00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008dc00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dd00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|  |TAMS 0x000000008de00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008df00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| O|  |TAMS 0x000000008e000000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e100000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| O|  |TAMS 0x000000008e200000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| O|  |TAMS 0x000000008e300000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| O|  |TAMS 0x000000008e400000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| O|  |TAMS 0x000000008e500000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%|HS|  |TAMS 0x000000008e600000| PB 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%|HC|  |TAMS 0x000000008e700000| PB 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| O|  |TAMS 0x000000008e800000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| O|  |TAMS 0x000000008e900000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%|HS|  |TAMS 0x000000008ea00000| PB 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%|HC|  |TAMS 0x000000008eb00000| PB 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%|HS|  |TAMS 0x000000008ec00000| PB 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%|HC|  |TAMS 0x000000008ed00000| PB 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| O|  |TAMS 0x000000008ee00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%|HS|  |TAMS 0x000000008ef00000| PB 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%|HC|  |TAMS 0x000000008f000000| PB 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| O|  |TAMS 0x000000008f100000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%|HS|  |TAMS 0x000000008f200000| PB 0x000000008f100000| Complete 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%|HS|  |TAMS 0x000000008f300000| PB 0x000000008f200000| Complete 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%|HC|  |TAMS 0x000000008f400000| PB 0x000000008f300000| Complete 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| O|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| O|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| O|  |TAMS 0x000000008f700000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| O|  |TAMS 0x000000008f800000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%|HS|  |TAMS 0x000000008f900000| PB 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%|HC|  |TAMS 0x000000008fa00000| PB 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%|HC|  |TAMS 0x000000008fb00000| PB 0x000000008fa00000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| O|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| O|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| O|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%| O|  |TAMS 0x000000008ff00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%| O|  |TAMS 0x0000000090000000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| O|  |TAMS 0x0000000090100000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%|HS|  |TAMS 0x0000000090200000| PB 0x0000000090100000| Complete 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%|HC|  |TAMS 0x0000000090300000| PB 0x0000000090200000| Complete 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%|HC|  |TAMS 0x0000000090400000| PB 0x0000000090300000| Complete 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| O|  |TAMS 0x0000000090500000| PB 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%|HS|  |TAMS 0x0000000090600000| PB 0x0000000090500000| Complete 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| O|  |TAMS 0x0000000090600000| PB 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| O|  |TAMS 0x0000000090700000| PB 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| O|  |TAMS 0x0000000090800000| PB 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| O|  |TAMS 0x0000000090900000| PB 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| O|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%|HS|  |TAMS 0x0000000090c00000| PB 0x0000000090b00000| Complete 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%|HC|  |TAMS 0x0000000090d00000| PB 0x0000000090c00000| Complete 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%|HC|  |TAMS 0x0000000090e00000| PB 0x0000000090d00000| Complete 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%|HC|  |TAMS 0x0000000090f00000| PB 0x0000000090e00000| Complete 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%|HC|  |TAMS 0x0000000091000000| PB 0x0000000090f00000| Complete 
| 272|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%|HC|  |TAMS 0x0000000091100000| PB 0x0000000091000000| Complete 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%|HC|  |TAMS 0x0000000091200000| PB 0x0000000091100000| Complete 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| O|  |TAMS 0x0000000091300000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| O|  |TAMS 0x0000000091400000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| O|  |TAMS 0x0000000091500000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| O|  |TAMS 0x0000000091600000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| O|  |TAMS 0x0000000091700000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| O|  |TAMS 0x0000000091800000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| O|  |TAMS 0x0000000091900000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| O|  |TAMS 0x0000000091a00000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| O|  |TAMS 0x0000000091b00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| O|  |TAMS 0x0000000091c00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| O|  |TAMS 0x0000000091d00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| O|  |TAMS 0x0000000091e00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| O|  |TAMS 0x0000000091f00000| PB 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| O|  |TAMS 0x0000000092000000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%| O|  |TAMS 0x0000000092100000| PB 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092200000, 0x0000000092200000|100%| O|  |TAMS 0x0000000092200000| PB 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092300000, 0x0000000092300000|100%| O|  |TAMS 0x0000000092300000| PB 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092400000, 0x0000000092400000|100%| O|  |TAMS 0x0000000092400000| PB 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%| O|  |TAMS 0x0000000092500000| PB 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092600000, 0x0000000092600000|100%| O|  |TAMS 0x0000000092600000| PB 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092700000, 0x0000000092700000|100%| O|  |TAMS 0x0000000092700000| PB 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092800000, 0x0000000092800000|100%| O|  |TAMS 0x0000000092800000| PB 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092900000, 0x0000000092900000|100%| O|  |TAMS 0x0000000092900000| PB 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092a00000, 0x0000000092a00000|100%| O|  |TAMS 0x0000000092980000| PB 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%| O|  |TAMS 0x0000000092a00000| PB 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| O|  |TAMS 0x0000000092b00000| PB 0x0000000092b00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%| O|  |TAMS 0x0000000092c00000| PB 0x0000000092c00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092e00000, 0x0000000092e00000|100%| O|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092f00000, 0x0000000092f00000|100%| O|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000093000000, 0x0000000093000000|100%| O|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093100000, 0x0000000093100000|100%| O|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093200000, 0x0000000093200000|100%| O|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%| O|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%| O|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%| O|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093600000, 0x0000000093600000|100%| O|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093700000, 0x0000000093700000|100%| O|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Untracked 
| 311|0x0000000093700000, 0x0000000093800000, 0x0000000093800000|100%| O|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093900000, 0x0000000093900000|100%| O|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093a00000, 0x0000000093a00000|100%| O|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093b00000, 0x0000000093b00000|100%| O|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093c00000, 0x0000000093c00000|100%| O|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093d00000, 0x0000000093d00000|100%| O|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093e00000, 0x0000000093e00000|100%| O|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093f00000, 0x0000000093f00000|100%| O|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000094000000, 0x0000000094000000|100%| O|  |TAMS 0x0000000093f00000| PB 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094000000, 0x0000000094100000|  0%| F|  |TAMS 0x0000000094000000| PB 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094200000, 0x0000000094300000|  0%| F|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094700000, 0x0000000094800000|  0%| F|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Untracked 
| 328|0x0000000094800000, 0x0000000094800000, 0x0000000094900000|  0%| F|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Untracked 
| 329|0x0000000094900000, 0x0000000094900000, 0x0000000094a00000|  0%| F|  |TAMS 0x0000000094900000| PB 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094a00000, 0x0000000094b00000|  0%| F|  |TAMS 0x0000000094a00000| PB 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094b00000, 0x0000000094c00000|  0%| F|  |TAMS 0x0000000094b00000| PB 0x0000000094b00000| Untracked 
| 332|0x0000000094c00000, 0x0000000094c00000, 0x0000000094d00000|  0%| F|  |TAMS 0x0000000094c00000| PB 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094d00000, 0x0000000094e00000|  0%| F|  |TAMS 0x0000000094d00000| PB 0x0000000094d00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094e00000, 0x0000000094f00000|  0%| F|  |TAMS 0x0000000094e00000| PB 0x0000000094e00000| Untracked 
| 335|0x0000000094f00000, 0x0000000094f00000, 0x0000000095000000|  0%| F|  |TAMS 0x0000000094f00000| PB 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095000000, 0x0000000095100000|  0%| F|  |TAMS 0x0000000095000000| PB 0x0000000095000000| Untracked 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000| PB 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095200000, 0x0000000095300000|  0%| F|  |TAMS 0x0000000095200000| PB 0x0000000095200000| Untracked 
| 339|0x0000000095300000, 0x0000000095400000, 0x0000000095400000|100%| O|  |TAMS 0x0000000095300000| PB 0x0000000095300000| Untracked 
| 340|0x0000000095400000, 0x0000000095500000, 0x0000000095500000|100%| O|  |TAMS 0x0000000095400000| PB 0x0000000095400000| Untracked 
| 341|0x0000000095500000, 0x0000000095600000, 0x0000000095600000|100%| O|  |TAMS 0x0000000095500000| PB 0x0000000095500000| Untracked 
| 342|0x0000000095600000, 0x0000000095700000, 0x0000000095700000|100%| O|  |TAMS 0x0000000095600000| PB 0x0000000095600000| Untracked 
| 343|0x0000000095700000, 0x0000000095800000, 0x0000000095800000|100%| O|  |TAMS 0x0000000095700000| PB 0x0000000095700000| Untracked 
| 344|0x0000000095800000, 0x0000000095900000, 0x0000000095900000|100%| O|  |TAMS 0x0000000095800000| PB 0x0000000095800000| Untracked 
| 345|0x0000000095900000, 0x0000000095a00000, 0x0000000095a00000|100%| O|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095b00000, 0x0000000095b00000|100%| O|  |TAMS 0x0000000095a00000| PB 0x0000000095a00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095c00000, 0x0000000095c00000|100%| O|  |TAMS 0x0000000095b00000| PB 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095d00000, 0x0000000095d00000|100%| O|  |TAMS 0x0000000095c00000| PB 0x0000000095c00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095e00000, 0x0000000095e00000|100%| O|  |TAMS 0x0000000095d00000| PB 0x0000000095d00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095f00000, 0x0000000095f00000|100%| O|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f80000, 0x0000000096000000| 50%| O|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked 
| 352|0x0000000096000000, 0x0000000096000000, 0x0000000096100000|  0%| F|  |TAMS 0x0000000096000000| PB 0x0000000096000000| Untracked 
| 353|0x0000000096100000, 0x0000000096100000, 0x0000000096200000|  0%| F|  |TAMS 0x0000000096100000| PB 0x0000000096100000| Untracked 
| 354|0x0000000096200000, 0x0000000096200000, 0x0000000096300000|  0%| F|  |TAMS 0x0000000096200000| PB 0x0000000096200000| Untracked 
| 355|0x0000000096300000, 0x0000000096300000, 0x0000000096400000|  0%| F|  |TAMS 0x0000000096300000| PB 0x0000000096300000| Untracked 
| 356|0x0000000096400000, 0x0000000096400000, 0x0000000096500000|  0%| F|  |TAMS 0x0000000096400000| PB 0x0000000096400000| Untracked 
| 357|0x0000000096500000, 0x0000000096500000, 0x0000000096600000|  0%| F|  |TAMS 0x0000000096500000| PB 0x0000000096500000| Untracked 
| 358|0x0000000096600000, 0x0000000096600000, 0x0000000096700000|  0%| F|  |TAMS 0x0000000096600000| PB 0x0000000096600000| Untracked 
| 359|0x0000000096700000, 0x0000000096700000, 0x0000000096800000|  0%| F|  |TAMS 0x0000000096700000| PB 0x0000000096700000| Untracked 
| 360|0x0000000096800000, 0x0000000096800000, 0x0000000096900000|  0%| F|  |TAMS 0x0000000096800000| PB 0x0000000096800000| Untracked 
| 361|0x0000000096900000, 0x0000000096900000, 0x0000000096a00000|  0%| F|  |TAMS 0x0000000096900000| PB 0x0000000096900000| Untracked 
| 362|0x0000000096a00000, 0x0000000096b00000, 0x0000000096b00000|100%| S|CS|TAMS 0x0000000096a00000| PB 0x0000000096a00000| Complete 
| 363|0x0000000096b00000, 0x0000000096c00000, 0x0000000096c00000|100%| S|CS|TAMS 0x0000000096b00000| PB 0x0000000096b00000| Complete 
| 364|0x0000000096c00000, 0x0000000096d00000, 0x0000000096d00000|100%| S|CS|TAMS 0x0000000096c00000| PB 0x0000000096c00000| Complete 
| 365|0x0000000096d00000, 0x0000000096e00000, 0x0000000096e00000|100%| S|CS|TAMS 0x0000000096d00000| PB 0x0000000096d00000| Complete 
| 366|0x0000000096e00000, 0x0000000096f00000, 0x0000000096f00000|100%| S|CS|TAMS 0x0000000096e00000| PB 0x0000000096e00000| Complete 
| 367|0x0000000096f00000, 0x0000000097000000, 0x0000000097000000|100%| S|CS|TAMS 0x0000000096f00000| PB 0x0000000096f00000| Complete 
| 368|0x0000000097000000, 0x0000000097100000, 0x0000000097100000|100%| S|CS|TAMS 0x0000000097000000| PB 0x0000000097000000| Complete 
| 369|0x0000000097100000, 0x0000000097200000, 0x0000000097200000|100%| S|CS|TAMS 0x0000000097100000| PB 0x0000000097100000| Complete 
| 370|0x0000000097200000, 0x0000000097300000, 0x0000000097300000|100%| S|CS|TAMS 0x0000000097200000| PB 0x0000000097200000| Complete 
| 371|0x0000000097300000, 0x0000000097400000, 0x0000000097400000|100%| S|CS|TAMS 0x0000000097300000| PB 0x0000000097300000| Complete 
| 372|0x0000000097400000, 0x0000000097500000, 0x0000000097500000|100%| S|CS|TAMS 0x0000000097400000| PB 0x0000000097400000| Complete 
| 373|0x0000000097500000, 0x0000000097600000, 0x0000000097600000|100%| S|CS|TAMS 0x0000000097500000| PB 0x0000000097500000| Complete 
| 374|0x0000000097600000, 0x0000000097700000, 0x0000000097700000|100%| S|CS|TAMS 0x0000000097600000| PB 0x0000000097600000| Complete 
| 375|0x0000000097700000, 0x0000000097800000, 0x0000000097800000|100%| S|CS|TAMS 0x0000000097700000| PB 0x0000000097700000| Complete 
| 376|0x0000000097800000, 0x0000000097900000, 0x0000000097900000|100%| S|CS|TAMS 0x0000000097800000| PB 0x0000000097800000| Complete 
| 377|0x0000000097900000, 0x0000000097a00000, 0x0000000097a00000|100%| S|CS|TAMS 0x0000000097900000| PB 0x0000000097900000| Complete 
| 378|0x0000000097a00000, 0x0000000097b00000, 0x0000000097b00000|100%| S|CS|TAMS 0x0000000097a00000| PB 0x0000000097a00000| Complete 
| 379|0x0000000097b00000, 0x0000000097b00000, 0x0000000097c00000|  0%| F|  |TAMS 0x0000000097b00000| PB 0x0000000097b00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097c00000, 0x0000000097d00000|  0%| F|  |TAMS 0x0000000097c00000| PB 0x0000000097c00000| Untracked 
| 381|0x0000000097d00000, 0x0000000097d00000, 0x0000000097e00000|  0%| F|  |TAMS 0x0000000097d00000| PB 0x0000000097d00000| Untracked 
| 382|0x0000000097e00000, 0x0000000097e00000, 0x0000000097f00000|  0%| F|  |TAMS 0x0000000097e00000| PB 0x0000000097e00000| Untracked 
| 383|0x0000000097f00000, 0x0000000097f00000, 0x0000000098000000|  0%| F|  |TAMS 0x0000000097f00000| PB 0x0000000097f00000| Untracked 
| 384|0x0000000098000000, 0x0000000098000000, 0x0000000098100000|  0%| F|  |TAMS 0x0000000098000000| PB 0x0000000098000000| Untracked 
| 385|0x0000000098100000, 0x0000000098100000, 0x0000000098200000|  0%| F|  |TAMS 0x0000000098100000| PB 0x0000000098100000| Untracked 
| 386|0x0000000098200000, 0x0000000098200000, 0x0000000098300000|  0%| F|  |TAMS 0x0000000098200000| PB 0x0000000098200000| Untracked 
| 387|0x0000000098300000, 0x0000000098300000, 0x0000000098400000|  0%| F|  |TAMS 0x0000000098300000| PB 0x0000000098300000| Untracked 
| 388|0x0000000098400000, 0x0000000098400000, 0x0000000098500000|  0%| F|  |TAMS 0x0000000098400000| PB 0x0000000098400000| Untracked 
| 389|0x0000000098500000, 0x0000000098500000, 0x0000000098600000|  0%| F|  |TAMS 0x0000000098500000| PB 0x0000000098500000| Untracked 
| 390|0x0000000098600000, 0x0000000098600000, 0x0000000098700000|  0%| F|  |TAMS 0x0000000098600000| PB 0x0000000098600000| Untracked 
| 391|0x0000000098700000, 0x0000000098700000, 0x0000000098800000|  0%| F|  |TAMS 0x0000000098700000| PB 0x0000000098700000| Untracked 
| 392|0x0000000098800000, 0x0000000098800000, 0x0000000098900000|  0%| F|  |TAMS 0x0000000098800000| PB 0x0000000098800000| Untracked 
| 393|0x0000000098900000, 0x0000000098900000, 0x0000000098a00000|  0%| F|  |TAMS 0x0000000098900000| PB 0x0000000098900000| Untracked 
| 394|0x0000000098a00000, 0x0000000098a00000, 0x0000000098b00000|  0%| F|  |TAMS 0x0000000098a00000| PB 0x0000000098a00000| Untracked 
| 395|0x0000000098b00000, 0x0000000098b00000, 0x0000000098c00000|  0%| F|  |TAMS 0x0000000098b00000| PB 0x0000000098b00000| Untracked 
| 396|0x0000000098c00000, 0x0000000098c00000, 0x0000000098d00000|  0%| F|  |TAMS 0x0000000098c00000| PB 0x0000000098c00000| Untracked 
| 397|0x0000000098d00000, 0x0000000098d00000, 0x0000000098e00000|  0%| F|  |TAMS 0x0000000098d00000| PB 0x0000000098d00000| Untracked 
| 398|0x0000000098e00000, 0x0000000098e00000, 0x0000000098f00000|  0%| F|  |TAMS 0x0000000098e00000| PB 0x0000000098e00000| Untracked 
| 399|0x0000000098f00000, 0x0000000098f00000, 0x0000000099000000|  0%| F|  |TAMS 0x0000000098f00000| PB 0x0000000098f00000| Untracked 
| 400|0x0000000099000000, 0x0000000099000000, 0x0000000099100000|  0%| F|  |TAMS 0x0000000099000000| PB 0x0000000099000000| Untracked 
| 401|0x0000000099100000, 0x0000000099100000, 0x0000000099200000|  0%| F|  |TAMS 0x0000000099100000| PB 0x0000000099100000| Untracked 
| 402|0x0000000099200000, 0x0000000099200000, 0x0000000099300000|  0%| F|  |TAMS 0x0000000099200000| PB 0x0000000099200000| Untracked 
| 403|0x0000000099300000, 0x0000000099300000, 0x0000000099400000|  0%| F|  |TAMS 0x0000000099300000| PB 0x0000000099300000| Untracked 
| 404|0x0000000099400000, 0x0000000099400000, 0x0000000099500000|  0%| F|  |TAMS 0x0000000099400000| PB 0x0000000099400000| Untracked 
| 405|0x0000000099500000, 0x0000000099500000, 0x0000000099600000|  0%| F|  |TAMS 0x0000000099500000| PB 0x0000000099500000| Untracked 
| 406|0x0000000099600000, 0x0000000099600000, 0x0000000099700000|  0%| F|  |TAMS 0x0000000099600000| PB 0x0000000099600000| Untracked 
| 407|0x0000000099700000, 0x0000000099700000, 0x0000000099800000|  0%| F|  |TAMS 0x0000000099700000| PB 0x0000000099700000| Untracked 
| 408|0x0000000099800000, 0x0000000099800000, 0x0000000099900000|  0%| F|  |TAMS 0x0000000099800000| PB 0x0000000099800000| Untracked 
| 409|0x0000000099900000, 0x0000000099900000, 0x0000000099a00000|  0%| F|  |TAMS 0x0000000099900000| PB 0x0000000099900000| Untracked 
| 410|0x0000000099a00000, 0x0000000099a00000, 0x0000000099b00000|  0%| F|  |TAMS 0x0000000099a00000| PB 0x0000000099a00000| Untracked 
| 411|0x0000000099b00000, 0x0000000099b00000, 0x0000000099c00000|  0%| F|  |TAMS 0x0000000099b00000| PB 0x0000000099b00000| Untracked 
| 412|0x0000000099c00000, 0x0000000099c00000, 0x0000000099d00000|  0%| F|  |TAMS 0x0000000099c00000| PB 0x0000000099c00000| Untracked 
| 413|0x0000000099d00000, 0x0000000099d00000, 0x0000000099e00000|  0%| F|  |TAMS 0x0000000099d00000| PB 0x0000000099d00000| Untracked 
| 414|0x0000000099e00000, 0x0000000099e00000, 0x0000000099f00000|  0%| F|  |TAMS 0x0000000099e00000| PB 0x0000000099e00000| Untracked 
| 415|0x0000000099f00000, 0x0000000099f00000, 0x000000009a000000|  0%| F|  |TAMS 0x0000000099f00000| PB 0x0000000099f00000| Untracked 
| 416|0x000000009a000000, 0x000000009a000000, 0x000000009a100000|  0%| F|  |TAMS 0x000000009a000000| PB 0x000000009a000000| Untracked 
| 417|0x000000009a100000, 0x000000009a100000, 0x000000009a200000|  0%| F|  |TAMS 0x000000009a100000| PB 0x000000009a100000| Untracked 
| 418|0x000000009a200000, 0x000000009a200000, 0x000000009a300000|  0%| F|  |TAMS 0x000000009a200000| PB 0x000000009a200000| Untracked 
| 419|0x000000009a300000, 0x000000009a300000, 0x000000009a400000|  0%| F|  |TAMS 0x000000009a300000| PB 0x000000009a300000| Untracked 
| 420|0x000000009a400000, 0x000000009a400000, 0x000000009a500000|  0%| F|  |TAMS 0x000000009a400000| PB 0x000000009a400000| Untracked 
| 421|0x000000009a500000, 0x000000009a500000, 0x000000009a600000|  0%| F|  |TAMS 0x000000009a500000| PB 0x000000009a500000| Untracked 
| 422|0x000000009a600000, 0x000000009a600000, 0x000000009a700000|  0%| F|  |TAMS 0x000000009a600000| PB 0x000000009a600000| Untracked 
| 423|0x000000009a700000, 0x000000009a700000, 0x000000009a800000|  0%| F|  |TAMS 0x000000009a700000| PB 0x000000009a700000| Untracked 
| 424|0x000000009a800000, 0x000000009a800000, 0x000000009a900000|  0%| F|  |TAMS 0x000000009a800000| PB 0x000000009a800000| Untracked 
| 425|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000| PB 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ab00000, 0x000000009ac00000|  0%| F|  |TAMS 0x000000009ab00000| PB 0x000000009ab00000| Untracked 
| 428|0x000000009ac00000, 0x000000009ac00000, 0x000000009ad00000|  0%| F|  |TAMS 0x000000009ac00000| PB 0x000000009ac00000| Untracked 
| 429|0x000000009ad00000, 0x000000009ad00000, 0x000000009ae00000|  0%| F|  |TAMS 0x000000009ad00000| PB 0x000000009ad00000| Untracked 
| 430|0x000000009ae00000, 0x000000009ae00000, 0x000000009af00000|  0%| F|  |TAMS 0x000000009ae00000| PB 0x000000009ae00000| Untracked 
| 431|0x000000009af00000, 0x000000009af00000, 0x000000009b000000|  0%| F|  |TAMS 0x000000009af00000| PB 0x000000009af00000| Untracked 
| 432|0x000000009b000000, 0x000000009b000000, 0x000000009b100000|  0%| F|  |TAMS 0x000000009b000000| PB 0x000000009b000000| Untracked 
| 433|0x000000009b100000, 0x000000009b100000, 0x000000009b200000|  0%| F|  |TAMS 0x000000009b100000| PB 0x000000009b100000| Untracked 
| 434|0x000000009b200000, 0x000000009b200000, 0x000000009b300000|  0%| F|  |TAMS 0x000000009b200000| PB 0x000000009b200000| Untracked 
| 435|0x000000009b300000, 0x000000009b300000, 0x000000009b400000|  0%| F|  |TAMS 0x000000009b300000| PB 0x000000009b300000| Untracked 
| 436|0x000000009b400000, 0x000000009b400000, 0x000000009b500000|  0%| F|  |TAMS 0x000000009b400000| PB 0x000000009b400000| Untracked 
| 437|0x000000009b500000, 0x000000009b500000, 0x000000009b600000|  0%| F|  |TAMS 0x000000009b500000| PB 0x000000009b500000| Untracked 
| 438|0x000000009b600000, 0x000000009b600000, 0x000000009b700000|  0%| F|  |TAMS 0x000000009b600000| PB 0x000000009b600000| Untracked 
| 439|0x000000009b700000, 0x000000009b700000, 0x000000009b800000|  0%| F|  |TAMS 0x000000009b700000| PB 0x000000009b700000| Untracked 
| 440|0x000000009b800000, 0x000000009b800000, 0x000000009b900000|  0%| F|  |TAMS 0x000000009b800000| PB 0x000000009b800000| Untracked 
| 441|0x000000009b900000, 0x000000009b900000, 0x000000009ba00000|  0%| F|  |TAMS 0x000000009b900000| PB 0x000000009b900000| Untracked 
| 442|0x000000009ba00000, 0x000000009ba00000, 0x000000009bb00000|  0%| F|  |TAMS 0x000000009ba00000| PB 0x000000009ba00000| Untracked 
| 443|0x000000009bb00000, 0x000000009bb00000, 0x000000009bc00000|  0%| F|  |TAMS 0x000000009bb00000| PB 0x000000009bb00000| Untracked 
| 444|0x000000009bc00000, 0x000000009bc00000, 0x000000009bd00000|  0%| F|  |TAMS 0x000000009bc00000| PB 0x000000009bc00000| Untracked 
| 445|0x000000009bd00000, 0x000000009bd00000, 0x000000009be00000|  0%| F|  |TAMS 0x000000009bd00000| PB 0x000000009bd00000| Untracked 
| 446|0x000000009be00000, 0x000000009be00000, 0x000000009bf00000|  0%| F|  |TAMS 0x000000009be00000| PB 0x000000009be00000| Untracked 
| 447|0x000000009bf00000, 0x000000009bf00000, 0x000000009c000000|  0%| F|  |TAMS 0x000000009bf00000| PB 0x000000009bf00000| Untracked 
| 448|0x000000009c000000, 0x000000009c000000, 0x000000009c100000|  0%| F|  |TAMS 0x000000009c000000| PB 0x000000009c000000| Untracked 
| 449|0x000000009c100000, 0x000000009c100000, 0x000000009c200000|  0%| F|  |TAMS 0x000000009c100000| PB 0x000000009c100000| Untracked 
| 450|0x000000009c200000, 0x000000009c200000, 0x000000009c300000|  0%| F|  |TAMS 0x000000009c200000| PB 0x000000009c200000| Untracked 
| 451|0x000000009c300000, 0x000000009c300000, 0x000000009c400000|  0%| F|  |TAMS 0x000000009c300000| PB 0x000000009c300000| Untracked 
| 452|0x000000009c400000, 0x000000009c400000, 0x000000009c500000|  0%| F|  |TAMS 0x000000009c400000| PB 0x000000009c400000| Untracked 
| 453|0x000000009c500000, 0x000000009c500000, 0x000000009c600000|  0%| F|  |TAMS 0x000000009c500000| PB 0x000000009c500000| Untracked 
| 454|0x000000009c600000, 0x000000009c600000, 0x000000009c700000|  0%| F|  |TAMS 0x000000009c600000| PB 0x000000009c600000| Untracked 
| 455|0x000000009c700000, 0x000000009c700000, 0x000000009c800000|  0%| F|  |TAMS 0x000000009c700000| PB 0x000000009c700000| Untracked 
| 456|0x000000009c800000, 0x000000009c800000, 0x000000009c900000|  0%| F|  |TAMS 0x000000009c800000| PB 0x000000009c800000| Untracked 
| 457|0x000000009c900000, 0x000000009c900000, 0x000000009ca00000|  0%| F|  |TAMS 0x000000009c900000| PB 0x000000009c900000| Untracked 
| 458|0x000000009ca00000, 0x000000009ca00000, 0x000000009cb00000|  0%| F|  |TAMS 0x000000009ca00000| PB 0x000000009ca00000| Untracked 
| 459|0x000000009cb00000, 0x000000009cb00000, 0x000000009cc00000|  0%| F|  |TAMS 0x000000009cb00000| PB 0x000000009cb00000| Untracked 
| 460|0x000000009cc00000, 0x000000009cc00000, 0x000000009cd00000|  0%| F|  |TAMS 0x000000009cc00000| PB 0x000000009cc00000| Untracked 
| 461|0x000000009cd00000, 0x000000009cd00000, 0x000000009ce00000|  0%| F|  |TAMS 0x000000009cd00000| PB 0x000000009cd00000| Untracked 
| 462|0x000000009ce00000, 0x000000009ce00000, 0x000000009cf00000|  0%| F|  |TAMS 0x000000009ce00000| PB 0x000000009ce00000| Untracked 
| 463|0x000000009cf00000, 0x000000009cf00000, 0x000000009d000000|  0%| F|  |TAMS 0x000000009cf00000| PB 0x000000009cf00000| Untracked 
| 464|0x000000009d000000, 0x000000009d000000, 0x000000009d100000|  0%| F|  |TAMS 0x000000009d000000| PB 0x000000009d000000| Untracked 
| 465|0x000000009d100000, 0x000000009d100000, 0x000000009d200000|  0%| F|  |TAMS 0x000000009d100000| PB 0x000000009d100000| Untracked 
| 466|0x000000009d200000, 0x000000009d200000, 0x000000009d300000|  0%| F|  |TAMS 0x000000009d200000| PB 0x000000009d200000| Untracked 
| 467|0x000000009d300000, 0x000000009d300000, 0x000000009d400000|  0%| F|  |TAMS 0x000000009d300000| PB 0x000000009d300000| Untracked 
| 468|0x000000009d400000, 0x000000009d400000, 0x000000009d500000|  0%| F|  |TAMS 0x000000009d400000| PB 0x000000009d400000| Untracked 
| 469|0x000000009d500000, 0x000000009d500000, 0x000000009d600000|  0%| F|  |TAMS 0x000000009d500000| PB 0x000000009d500000| Untracked 
| 470|0x000000009d600000, 0x000000009d600000, 0x000000009d700000|  0%| F|  |TAMS 0x000000009d600000| PB 0x000000009d600000| Untracked 
| 471|0x000000009d700000, 0x000000009d700000, 0x000000009d800000|  0%| F|  |TAMS 0x000000009d700000| PB 0x000000009d700000| Untracked 
| 472|0x000000009d800000, 0x000000009d800000, 0x000000009d900000|  0%| F|  |TAMS 0x000000009d800000| PB 0x000000009d800000| Untracked 
| 473|0x000000009d900000, 0x000000009d900000, 0x000000009da00000|  0%| F|  |TAMS 0x000000009d900000| PB 0x000000009d900000| Untracked 
| 474|0x000000009da00000, 0x000000009da00000, 0x000000009db00000|  0%| F|  |TAMS 0x000000009da00000| PB 0x000000009da00000| Untracked 
| 475|0x000000009db00000, 0x000000009db00000, 0x000000009dc00000|  0%| F|  |TAMS 0x000000009db00000| PB 0x000000009db00000| Untracked 
| 476|0x000000009dc00000, 0x000000009dc00000, 0x000000009dd00000|  0%| F|  |TAMS 0x000000009dc00000| PB 0x000000009dc00000| Untracked 
| 477|0x000000009dd00000, 0x000000009dd00000, 0x000000009de00000|  0%| F|  |TAMS 0x000000009dd00000| PB 0x000000009dd00000| Untracked 
| 478|0x000000009de00000, 0x000000009de00000, 0x000000009df00000|  0%| F|  |TAMS 0x000000009de00000| PB 0x000000009de00000| Untracked 
| 479|0x000000009df00000, 0x000000009df00000, 0x000000009e000000|  0%| F|  |TAMS 0x000000009df00000| PB 0x000000009df00000| Untracked 
| 480|0x000000009e000000, 0x000000009e000000, 0x000000009e100000|  0%| F|  |TAMS 0x000000009e000000| PB 0x000000009e000000| Untracked 
| 481|0x000000009e100000, 0x000000009e100000, 0x000000009e200000|  0%| F|  |TAMS 0x000000009e100000| PB 0x000000009e100000| Untracked 
| 482|0x000000009e200000, 0x000000009e200000, 0x000000009e300000|  0%| F|  |TAMS 0x000000009e200000| PB 0x000000009e200000| Untracked 
| 483|0x000000009e300000, 0x000000009e300000, 0x000000009e400000|  0%| F|  |TAMS 0x000000009e300000| PB 0x000000009e300000| Untracked 
| 484|0x000000009e400000, 0x000000009e400000, 0x000000009e500000|  0%| F|  |TAMS 0x000000009e400000| PB 0x000000009e400000| Untracked 
| 485|0x000000009e500000, 0x000000009e500000, 0x000000009e600000|  0%| F|  |TAMS 0x000000009e500000| PB 0x000000009e500000| Untracked 
|2000|0x00000000fd000000, 0x00000000fd100000, 0x00000000fd100000|100%| O|  |TAMS 0x00000000fd100000| PB 0x00000000fd000000| Untracked 
|2001|0x00000000fd100000, 0x00000000fd100000, 0x00000000fd200000|  0%| F|  |TAMS 0x00000000fd100000| PB 0x00000000fd100000| Untracked 
|2002|0x00000000fd200000, 0x00000000fd300000, 0x00000000fd300000|100%| O|  |TAMS 0x00000000fd300000| PB 0x00000000fd200000| Untracked 
|2003|0x00000000fd300000, 0x00000000fd400000, 0x00000000fd400000|100%| O|  |TAMS 0x00000000fd400000| PB 0x00000000fd300000| Untracked 
|2004|0x00000000fd400000, 0x00000000fd500000, 0x00000000fd500000|100%| O|  |TAMS 0x00000000fd500000| PB 0x00000000fd400000| Untracked 
|2005|0x00000000fd500000, 0x00000000fd500000, 0x00000000fd600000|  0%| F|  |TAMS 0x00000000fd500000| PB 0x00000000fd500000| Untracked 
|2006|0x00000000fd600000, 0x00000000fd600000, 0x00000000fd700000|  0%| F|  |TAMS 0x00000000fd600000| PB 0x00000000fd600000| Untracked 
|2007|0x00000000fd700000, 0x00000000fd800000, 0x00000000fd800000|100%| O|  |TAMS 0x00000000fd800000| PB 0x00000000fd700000| Untracked 
|2008|0x00000000fd800000, 0x00000000fd900000, 0x00000000fd900000|100%| O|  |TAMS 0x00000000fd900000| PB 0x00000000fd800000| Untracked 
|2009|0x00000000fd900000, 0x00000000fda00000, 0x00000000fda00000|100%| O|  |TAMS 0x00000000fda00000| PB 0x00000000fd900000| Untracked 
|2010|0x00000000fda00000, 0x00000000fdb00000, 0x00000000fdb00000|100%| O|  |TAMS 0x00000000fdb00000| PB 0x00000000fda00000| Untracked 
|2011|0x00000000fdb00000, 0x00000000fdc00000, 0x00000000fdc00000|100%| O|  |TAMS 0x00000000fdc00000| PB 0x00000000fdb00000| Untracked 
|2012|0x00000000fdc00000, 0x00000000fdd00000, 0x00000000fdd00000|100%| O|  |TAMS 0x00000000fdd00000| PB 0x00000000fdc00000| Untracked 
|2013|0x00000000fdd00000, 0x00000000fde00000, 0x00000000fde00000|100%| O|  |TAMS 0x00000000fde00000| PB 0x00000000fdd00000| Untracked 
|2014|0x00000000fde00000, 0x00000000fdf00000, 0x00000000fdf00000|100%| O|  |TAMS 0x00000000fdf00000| PB 0x00000000fde00000| Untracked 
|2015|0x00000000fdf00000, 0x00000000fdf00000, 0x00000000fe000000|  0%| F|  |TAMS 0x00000000fdf00000| PB 0x00000000fdf00000| Untracked 
|2016|0x00000000fe000000, 0x00000000fe100000, 0x00000000fe100000|100%| O|  |TAMS 0x00000000fe100000| PB 0x00000000fe000000| Untracked 
|2017|0x00000000fe100000, 0x00000000fe200000, 0x00000000fe200000|100%| O|  |TAMS 0x00000000fe200000| PB 0x00000000fe100000| Untracked 
|2018|0x00000000fe200000, 0x00000000fe300000, 0x00000000fe300000|100%| O|  |TAMS 0x00000000fe300000| PB 0x00000000fe200000| Untracked 
|2019|0x00000000fe300000, 0x00000000fe400000, 0x00000000fe400000|100%| O|  |TAMS 0x00000000fe400000| PB 0x00000000fe300000| Untracked 
|2020|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| O|  |TAMS 0x00000000fe500000| PB 0x00000000fe400000| Untracked 
|2021|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| O|  |TAMS 0x00000000fe600000| PB 0x00000000fe500000| Untracked 
|2022|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| O|  |TAMS 0x00000000fe700000| PB 0x00000000fe600000| Untracked 
|2023|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| O|  |TAMS 0x00000000fe800000| PB 0x00000000fe700000| Untracked 
|2024|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| O|  |TAMS 0x00000000fe900000| PB 0x00000000fe800000| Untracked 
|2025|0x00000000fe900000, 0x00000000fe900000, 0x00000000fea00000|  0%| F|  |TAMS 0x00000000fe900000| PB 0x00000000fe900000| Untracked 
|2026|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| O|  |TAMS 0x00000000feb00000| PB 0x00000000fea00000| Untracked 
|2027|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| O|  |TAMS 0x00000000fec00000| PB 0x00000000feb00000| Untracked 
|2028|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| O|  |TAMS 0x00000000fed00000| PB 0x00000000fec00000| Untracked 
|2029|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| O|  |TAMS 0x00000000fee00000| PB 0x00000000fed00000| Untracked 
|2030|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| O|  |TAMS 0x00000000fef00000| PB 0x00000000fee00000| Untracked 
|2031|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| O|  |TAMS 0x00000000ff000000| PB 0x00000000fef00000| Untracked 
|2032|0x00000000ff000000, 0x00000000ff000000, 0x00000000ff100000|  0%| F|  |TAMS 0x00000000ff000000| PB 0x00000000ff000000| Untracked 
|2033|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| O|  |TAMS 0x00000000ff200000| PB 0x00000000ff100000| Untracked 
|2034|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| O|  |TAMS 0x00000000ff300000| PB 0x00000000ff200000| Untracked 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| O|  |TAMS 0x00000000ff400000| PB 0x00000000ff300000| Untracked 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| O|  |TAMS 0x00000000ff500000| PB 0x00000000ff400000| Untracked 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff500000| Untracked 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| O|  |TAMS 0x00000000ff700000| PB 0x00000000ff600000| Untracked 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| O|  |TAMS 0x00000000ff800000| PB 0x00000000ff700000| Untracked 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff800000| Untracked 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ff900000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffc00000| Untracked 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000ffe00000| Untracked 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x0000000100000000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x0000028a4d6d0000,0x0000028a4dad0000] _byte_map_base: 0x0000028a4d2d0000

Marking Bits: (CMBitMap*) 0x0000028a33c50240
 Bits: [0x0000028a4dad0000, 0x0000028a4fad0000)

Polling page: 0x0000028a33cc0000

Metaspace:

Usage:
  Non-class:    123.21 MB used.
      Class:     19.63 MB used.
       Both:    142.84 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     124.44 MB ( 97%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      20.88 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     145.31 MB ( 13%) committed. 

Chunk freelists:
   Non-Class:  3.55 MB
       Class:  11.17 MB
        Both:  14.72 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 230.75 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 4522.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 2323.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 10993.
num_chunk_merges: 9.
num_chunk_splits: 7251.
num_chunks_enlarged: 4643.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=13179Kb max_used=13179Kb free=106820Kb
 bounds [0x0000028a45340000, 0x0000028a46020000, 0x0000028a4c870000]
CodeHeap 'profiled nmethods': size=120000Kb used=32444Kb max_used=32444Kb free=87555Kb
 bounds [0x0000028a3d870000, 0x0000028a3f860000, 0x0000028a44da0000]
CodeHeap 'non-nmethods': size=5760Kb used=3072Kb max_used=3148Kb free=2687Kb
 bounds [0x0000028a44da0000, 0x0000028a450c0000, 0x0000028a45340000]
 total_blobs=18037 nmethods=16923 adapters=1017
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 56.888 Thread 0x0000028a5269ac30 nmethod 22701 0x0000028a3e021390 code [0x0000028a3e021520, 0x0000028a3e021630]
Event: 56.888 Thread 0x0000028a5269ac30 22774       3       com.android.tools.r8.internal.oS$$Lambda/0x0000028a5543c5b0::test (16 bytes)
Event: 56.888 Thread 0x0000028a5269ac30 nmethod 22774 0x0000028a3e020c10 code [0x0000028a3e020de0, 0x0000028a3e0211f0]
Event: 56.888 Thread 0x0000028a5269ac30 22775       3       com.android.tools.r8.internal.oS::a (28 bytes)
Event: 56.888 Thread 0x0000028a5269ac30 nmethod 22775 0x0000028a3e020690 code [0x0000028a3e020860, 0x0000028a3e020b18]
Event: 56.888 Thread 0x0000028a5269ac30 22733       3       com.android.tools.r8.internal.nu0$$Lambda/0x0000028a5540b460::test (8 bytes)
Event: 56.889 Thread 0x0000028a5269ac30 nmethod 22733 0x0000028a3e020010 code [0x0000028a3e0201c0, 0x0000028a3e0205a0]
Event: 56.889 Thread 0x0000028a5269ac30 22734       3       com.android.tools.r8.internal.BE::p1 (5 bytes)
Event: 56.889 Thread 0x0000028a5269ac30 nmethod 22734 0x0000028a3e01fb10 code [0x0000028a3e01fcc0, 0x0000028a3e01fec8]
Event: 56.889 Thread 0x0000028a5269ac30 22726       3       com.android.tools.r8.ir.optimize.a::<init> (14 bytes)
Event: 56.890 Thread 0x0000028a5269ac30 nmethod 22726 0x0000028a3e039a90 code [0x0000028a3e039ce0, 0x0000028a3e03a6d8]
Event: 56.890 Thread 0x0000028a5269ac30 22778       2       com.android.tools.r8.graph.U4::a (94 bytes)
Event: 56.891 Thread 0x0000028a5269ac30 nmethod 22778 0x0000028a3e039190 code [0x0000028a3e039380, 0x0000028a3e0397e0]
Event: 56.891 Thread 0x0000028a5269ac30 22779       2       com.android.tools.r8.synthesis.J::a (152 bytes)
Event: 56.892 Thread 0x0000028a5269ac30 nmethod 22779 0x0000028a3e038510 code [0x0000028a3e038780, 0x0000028a3e038d80]
Event: 56.892 Thread 0x0000028a5269ac30 22776       2       com.android.tools.r8.internal.Jz::a (38 bytes)
Event: 56.892 Thread 0x0000028a5269ac30 nmethod 22776 0x0000028a3e038190 code [0x0000028a3e038320, 0x0000028a3e038428]
Event: 56.892 Thread 0x0000028a5269ac30 22787       2       com.android.tools.r8.internal.Se0::<init> (95 bytes)
Event: 56.893 Thread 0x0000028a5269ac30 nmethod 22787 0x0000028a3e037610 code [0x0000028a3e037800, 0x0000028a3e037e30]
Event: 56.893 Thread 0x0000028a5269ac30 22785       2       com.android.tools.r8.internal.BE::a (851 bytes)

GC Heap History (20 events):
Event: 51.187 GC heap after
{Heap after GC invocations=54 (full 0):
 garbage-first heap   total 290816K, used 182403K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 126653K, committed 129152K, reserved 1179648K
  class space    used 16862K, committed 18112K, reserved 1048576K
}
Event: 51.985 GC heap before
{Heap before GC invocations=55 (full 0):
 garbage-first heap   total 314368K, used 261251K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 87 young (89088K), 9 survivors (9216K)
 Metaspace       used 126772K, committed 129280K, reserved 1179648K
  class space    used 16863K, committed 18112K, reserved 1048576K
}
Event: 51.996 GC heap after
{Heap after GC invocations=56 (full 0):
 garbage-first heap   total 314368K, used 187817K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 126772K, committed 129280K, reserved 1179648K
  class space    used 16863K, committed 18112K, reserved 1048576K
}
Event: 52.342 GC heap before
{Heap before GC invocations=56 (full 0):
 garbage-first heap   total 314368K, used 279977K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 101 young (103424K), 10 survivors (10240K)
 Metaspace       used 126807K, committed 129280K, reserved 1179648K
  class space    used 16864K, committed 18112K, reserved 1048576K
}
Event: 52.365 GC heap after
{Heap after GC invocations=57 (full 0):
 garbage-first heap   total 314368K, used 171494K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 126807K, committed 129280K, reserved 1179648K
  class space    used 16864K, committed 18112K, reserved 1048576K
}
Event: 52.878 GC heap before
{Heap before GC invocations=57 (full 0):
 garbage-first heap   total 314368K, used 277990K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 113 young (115712K), 8 survivors (8192K)
 Metaspace       used 126840K, committed 129344K, reserved 1179648K
  class space    used 16864K, committed 18112K, reserved 1048576K
}
Event: 52.887 GC heap after
{Heap after GC invocations=58 (full 0):
 garbage-first heap   total 314368K, used 173609K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 126840K, committed 129344K, reserved 1179648K
  class space    used 16864K, committed 18112K, reserved 1048576K
}
Event: 53.486 GC heap before
{Heap before GC invocations=58 (full 0):
 garbage-first heap   total 314368K, used 260649K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 97 young (99328K), 10 survivors (10240K)
 Metaspace       used 127173K, committed 129664K, reserved 1179648K
  class space    used 16922K, committed 18176K, reserved 1048576K
}
Event: 53.493 GC heap after
{Heap after GC invocations=59 (full 0):
 garbage-first heap   total 314368K, used 176309K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 127173K, committed 129664K, reserved 1179648K
  class space    used 16922K, committed 18176K, reserved 1048576K
}
Event: 54.463 GC heap before
{Heap before GC invocations=60 (full 0):
 garbage-first heap   total 347136K, used 325813K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 116 young (118784K), 10 survivors (10240K)
 Metaspace       used 131313K, committed 133824K, reserved 1179648K
  class space    used 17526K, committed 18752K, reserved 1048576K
}
Event: 54.476 GC heap after
{Heap after GC invocations=61 (full 0):
 garbage-first heap   total 364544K, used 237568K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 15 survivors (15360K)
 Metaspace       used 131313K, committed 133824K, reserved 1179648K
  class space    used 17526K, committed 18752K, reserved 1048576K
}
Event: 54.826 GC heap before
{Heap before GC invocations=61 (full 0):
 garbage-first heap   total 364544K, used 363520K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 86 young (88064K), 15 survivors (15360K)
 Metaspace       used 133524K, committed 136000K, reserved 1179648K
  class space    used 17814K, committed 19072K, reserved 1048576K
}
Event: 54.842 GC heap after
{Heap after GC invocations=62 (full 0):
 garbage-first heap   total 403456K, used 273110K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 133524K, committed 136000K, reserved 1179648K
  class space    used 17814K, committed 19072K, reserved 1048576K
}
Event: 55.282 GC heap before
{Heap before GC invocations=62 (full 0):
 garbage-first heap   total 403456K, used 328406K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 69 young (70656K), 13 survivors (13312K)
 Metaspace       used 137293K, committed 139776K, reserved 1179648K
  class space    used 18391K, committed 19648K, reserved 1048576K
}
Event: 55.297 GC heap after
{Heap after GC invocations=63 (full 0):
 garbage-first heap   total 403456K, used 285696K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 137293K, committed 139776K, reserved 1179648K
  class space    used 18391K, committed 19648K, reserved 1048576K
}
Event: 55.711 GC heap before
{Heap before GC invocations=64 (full 0):
 garbage-first heap   total 546816K, used 364544K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 86 young (88064K), 12 survivors (12288K)
 Metaspace       used 140161K, committed 142656K, reserved 1179648K
  class space    used 19046K, committed 20288K, reserved 1048576K
}
Event: 55.736 GC heap after
{Heap after GC invocations=65 (full 0):
 garbage-first heap   total 546816K, used 309248K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 140161K, committed 142656K, reserved 1179648K
  class space    used 19046K, committed 20288K, reserved 1048576K
}
Event: 56.403 GC heap before
{Heap before GC invocations=65 (full 0):
 garbage-first heap   total 546816K, used 471040K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 147 young (150528K), 11 survivors (11264K)
 Metaspace       used 145676K, committed 148160K, reserved 1179648K
  class space    used 20057K, committed 21312K, reserved 1048576K
}
Event: 56.444 GC heap after
{Heap after GC invocations=66 (full 0):
 garbage-first heap   total 546816K, used 352768K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 145676K, committed 148160K, reserved 1179648K
  class space    used 20057K, committed 21312K, reserved 1048576K
}
Event: 56.896 GC heap before
{Heap before GC invocations=66 (full 0):
 garbage-first heap   total 546816K, used 467456K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 132 young (135168K), 19 survivors (19456K)
 Metaspace       used 146264K, committed 148800K, reserved 1179648K
  class space    used 20101K, committed 21376K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.010 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.051 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.095 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.098 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.101 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.103 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.106 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.363 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.498 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 0.645 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 0.651 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 1.740 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.742 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 1.907 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 2.076 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 56.592 Thread 0x0000028aa742a410 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000028a45ed197c relative=0x000000000000085c
Event: 56.592 Thread 0x0000028aa742a410 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000028a45ed197c method=com.android.tools.r8.internal.Rq0.a(Ljava/util/function/Function;Ljava/util/function/Predicate;)Lcom/android/tools/r8/internal/Rq0; @ 16 c2
Event: 56.592 Thread 0x0000028aa742a410 DEOPT PACKING pc=0x0000028a45ed197c sp=0x000000f40e9fc230
Event: 56.592 Thread 0x0000028aa742a410 DEOPT UNPACKING pc=0x0000028a44df46a2 sp=0x000000f40e9fc1d8 mode 2
Event: 56.592 Thread 0x0000028aa742a410 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000028a45ed197c relative=0x000000000000085c
Event: 56.592 Thread 0x0000028aa742a410 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000028a45ed197c method=com.android.tools.r8.internal.Rq0.a(Ljava/util/function/Function;Ljava/util/function/Predicate;)Lcom/android/tools/r8/internal/Rq0; @ 16 c2
Event: 56.592 Thread 0x0000028aa742a410 DEOPT PACKING pc=0x0000028a45ed197c sp=0x000000f40e9fbf20
Event: 56.592 Thread 0x0000028aa742a410 DEOPT UNPACKING pc=0x0000028a44df46a2 sp=0x000000f40e9fbec8 mode 2
Event: 56.593 Thread 0x0000028aa742a410 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000028a4570e818 relative=0x0000000000000078
Event: 56.593 Thread 0x0000028aa742a410 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000028a4570e818 method=java.lang.invoke.Invokers$Holder.linkToTargetMethod(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 2 c2
Event: 56.593 Thread 0x0000028aa742a410 DEOPT PACKING pc=0x0000028a4570e818 sp=0x000000f40e9fc4e0
Event: 56.593 Thread 0x0000028aa742a410 DEOPT UNPACKING pc=0x0000028a44df46a2 sp=0x000000f40e9fc468 mode 2
Event: 56.730 Thread 0x0000028aa7423480 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000028a4570e818 relative=0x0000000000000078
Event: 56.730 Thread 0x0000028aa7423480 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000028a4570e818 method=java.lang.invoke.Invokers$Holder.linkToTargetMethod(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 2 c2
Event: 56.730 Thread 0x0000028aa7423480 DEOPT PACKING pc=0x0000028a4570e818 sp=0x000000f40eafc0c0
Event: 56.730 Thread 0x0000028aa7423480 DEOPT UNPACKING pc=0x0000028a44df46a2 sp=0x000000f40eafc048 mode 2
Event: 56.844 Thread 0x0000028aa7423480 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000028a4570e818 relative=0x0000000000000078
Event: 56.844 Thread 0x0000028aa7423480 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000028a4570e818 method=java.lang.invoke.Invokers$Holder.linkToTargetMethod(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 2 c2
Event: 56.844 Thread 0x0000028aa7423480 DEOPT PACKING pc=0x0000028a4570e818 sp=0x000000f40eafc0c0
Event: 56.844 Thread 0x0000028aa7423480 DEOPT UNPACKING pc=0x0000028a44df46a2 sp=0x000000f40eafc048 mode 2

Classes loaded (20 events):
Event: 55.961 Loading class java/util/stream/SliceOps$1
Event: 55.961 Loading class java/util/stream/SliceOps$1 done
Event: 55.963 Loading class java/util/stream/SliceOps$1$1
Event: 55.963 Loading class java/util/stream/SliceOps$1$1 done
Event: 55.970 Loading class java/util/PriorityQueue
Event: 55.970 Loading class java/util/PriorityQueue done
Event: 55.982 Loading class java/util/IdentityHashMap$ValueIterator
Event: 55.982 Loading class java/util/IdentityHashMap$ValueIterator done
Event: 56.007 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable
Event: 56.007 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable done
Event: 56.192 Loading class java/nio/ShortBuffer
Event: 56.192 Loading class java/nio/ShortBuffer done
Event: 56.197 Loading class java/nio/HeapShortBuffer
Event: 56.198 Loading class java/nio/HeapShortBuffer done
Event: 56.204 Loading class java/util/AbstractList$RandomAccessSubList
Event: 56.205 Loading class java/util/AbstractList$SubList
Event: 56.205 Loading class java/util/AbstractList$SubList done
Event: 56.205 Loading class java/util/AbstractList$RandomAccessSubList done
Event: 56.206 Loading class java/util/AbstractList$SubList$1
Event: 56.206 Loading class java/util/AbstractList$SubList$1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 55.801 Thread 0x0000028aa5346c40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000009d5dc0f8}: Found class java.lang.Object, but interface was expected> (0x000000009d5dc0f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 55.801 Thread 0x0000028aa5a958c0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000009d484078}: Found class java.lang.Object, but interface was expected> (0x000000009d484078) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 55.899 Thread 0x0000028a9c3de700 Exception <a 'java/lang/NoSuchMethodError'{0x000000009c4ec2b0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000009c4ec2b0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 55.899 Thread 0x0000028aa5a958c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000009b59c900}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000009b59c900) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 55.899 Thread 0x0000028aa5346c40 Exception <a 'java/lang/NoSuchMethodError'{0x000000009c72d318}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000009c72d318) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 55.899 Thread 0x0000028aa7423480 Exception <a 'java/lang/NoSuchMethodError'{0x000000009c4c43d0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000009c4c43d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 55.994 Thread 0x0000028a9c3de700 Exception <a 'java/lang/NoSuchMethodError'{0x00000000991f10f0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000991f10f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 55.994 Thread 0x0000028aa7423480 Exception <a 'java/lang/NoSuchMethodError'{0x0000000099230c80}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000099230c80) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 55.994 Thread 0x0000028aa5346c40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000992f77c8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000992f77c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 55.994 Thread 0x0000028aa5a958c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000991db100}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000991db100) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 55.994 Thread 0x0000028aa742a410 Exception <a 'java/lang/NoSuchMethodError'{0x00000000991fe7e0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000991fe7e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.024 Thread 0x0000028aa7425550 Exception <a 'java/lang/NoSuchMethodError'{0x0000000098846318}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000098846318) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.024 Thread 0x0000028aa53437c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000009882a9d8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000009882a9d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.025 Thread 0x0000028aa7423480 Exception <a 'java/lang/NoSuchMethodError'{0x0000000098866a38}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000098866a38) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.025 Thread 0x0000028aa4779480 Exception <a 'java/lang/NoSuchMethodError'{0x00000000989bb248}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000989bb248) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.025 Thread 0x0000028aa477d620 Exception <a 'java/lang/NoSuchMethodError'{0x0000000098a4f018}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000098a4f018) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.041 Thread 0x0000028aa7425550 Exception <a 'java/lang/NoSuchMethodError'{0x0000000098679608}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000098679608) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.167 Thread 0x0000028aa5a958c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000973c9598}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000973c9598) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.183 Thread 0x0000028aa5a958c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000097299420}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int)'> (0x0000000097299420) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 56.586 Thread 0x0000028aa742a410 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000009bf64888}: Found class java.lang.Object, but interface was expected> (0x000000009bf64888) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 56.227 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 56.384 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 56.402 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 56.402 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 56.445 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 56.578 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 56.819 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 56.820 Executing VM operation: ICBufferFull
Event: 56.835 Executing VM operation: ICBufferFull done
Event: 56.838 Executing VM operation: ICBufferFull
Event: 56.842 Executing VM operation: ICBufferFull done
Event: 56.843 Executing VM operation: ICBufferFull
Event: 56.846 Executing VM operation: ICBufferFull done
Event: 56.851 Executing VM operation: ICBufferFull
Event: 56.855 Executing VM operation: ICBufferFull done
Event: 56.856 Executing VM operation: ICBufferFull
Event: 56.865 Executing VM operation: ICBufferFull done
Event: 56.876 Executing VM operation: ICBufferFull
Event: 56.896 Executing VM operation: ICBufferFull done
Event: 56.896 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Events (20 events):
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a4574da10
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a456ab310
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a45694010
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a4558d210
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a4558ce90
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a45589b10
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a4551fc10
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a4551ee90
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a4554c390
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a454ef810
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a45396690
Event: 55.506 Thread 0x0000028a525b52b0 flushing nmethod 0x0000028a45392710
Event: 56.008 Thread 0x0000028aa4778760 Thread added: 0x0000028aa4778760
Event: 56.008 Thread 0x0000028aa477cf90 Thread added: 0x0000028aa477cf90
Event: 56.010 Thread 0x0000028aa477bbe0 Thread added: 0x0000028aa477bbe0
Event: 56.010 Thread 0x0000028aa4779480 Thread added: 0x0000028aa4779480
Event: 56.010 Thread 0x0000028aa477d620 Thread added: 0x0000028aa477d620
Event: 56.011 Thread 0x0000028aa4777a40 Thread added: 0x0000028aa4777a40
Event: 56.011 Thread 0x0000028aa4776690 Thread added: 0x0000028aa4776690
Event: 56.012 Thread 0x0000028aa477a830 Thread added: 0x0000028aa477a830


Dynamic libraries:
0x00007ff68f980000 - 0x00007ff68f990000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007fffd2a70000 - 0x00007fffd2c68000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffd0f10000 - 0x00007fffd0fd2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffd02b0000 - 0x00007fffd05a6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffd0710000 - 0x00007fffd0810000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fffb4da0000 - 0x00007fffb4db9000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007fffb4dc0000 - 0x00007fffb4ddb000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007fffd1e80000 - 0x00007fffd1f31000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffd1100000 - 0x00007fffd119e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fffd1270000 - 0x00007fffd130f000 	C:\WINDOWS\System32\sechost.dll
0x00007fffd1cc0000 - 0x00007fffd1de3000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffd01f0000 - 0x00007fffd0217000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fffd1b10000 - 0x00007fffd1cad000 	C:\WINDOWS\System32\USER32.dll
0x00007fffd09b0000 - 0x00007fffd09d2000 	C:\WINDOWS\System32\win32u.dll
0x00007fffbefc0000 - 0x00007fffbf25a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007fffd1310000 - 0x00007fffd133b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffd0890000 - 0x00007fffd09a9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffd0100000 - 0x00007fffd019d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fffc7ef0000 - 0x00007fffc7efa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fffd1f40000 - 0x00007fffd1f6f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fffa9870000 - 0x00007fffa987c000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007fff9ecd0000 - 0x00007fff9ed5e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007fff7d700000 - 0x00007fff7e417000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007fffd1090000 - 0x00007fffd10fb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fffcff60000 - 0x00007fffcffab000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fffc4da0000 - 0x00007fffc4dc7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffcff40000 - 0x00007fffcff52000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fffce970000 - 0x00007fffce982000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fffa8c10000 - 0x00007fffa8c1a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007fffce700000 - 0x00007fffce901000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffbe6d0000 - 0x00007fffbe704000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffd0220000 - 0x00007fffd02a2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fffa4e40000 - 0x00007fffa4e4f000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007fffa4780000 - 0x00007fffa479f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007fffd1340000 - 0x00007fffd1aae000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fffcdf50000 - 0x00007fffce6f3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fffd26b0000 - 0x00007fffd2a03000 	C:\WINDOWS\System32\combase.dll
0x00007fffcfa60000 - 0x00007fffcfa8b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007fffd11a0000 - 0x00007fffd126d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fffd0fe0000 - 0x00007fffd108d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fffd2180000 - 0x00007fffd21db000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fffd0030000 - 0x00007fffd0055000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff9d370000 - 0x00007fff9d447000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007fffa3fb0000 - 0x00007fffa3fc8000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007fffa7f90000 - 0x00007fffa7fa0000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007fffcc310000 - 0x00007fffcc41a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fffcf7c0000 - 0x00007fffcf82a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fffa3fd0000 - 0x00007fffa3fe6000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007fffa75f0000 - 0x00007fffa7600000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007fffc0060000 - 0x00007fffc0087000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007fffa0fe0000 - 0x00007fffa1124000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007fffa7190000 - 0x00007fffa719a000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007fffa6aa0000 - 0x00007fffa6aab000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007fffd0b40000 - 0x00007fffd0b48000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fffcf9b0000 - 0x00007fffcf9c8000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fffcf0e0000 - 0x00007fffcf118000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fffcffb0000 - 0x00007fffcffde000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fffcf9d0000 - 0x00007fffcf9dc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fffcf4a0000 - 0x00007fffcf4db000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fffd1ff0000 - 0x00007fffd1ff8000 	C:\WINDOWS\System32\NSI.dll
0x00007fffa59b0000 - 0x00007fffa59b9000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007fffa3f20000 - 0x00007fffa3f2e000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007fffd05b0000 - 0x00007fffd070d000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fffcfad0000 - 0x00007fffcfaf7000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fffcfa90000 - 0x00007fffcfacb000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fffc0050000 - 0x00007fffc0057000 	C:\WINDOWS\system32\wshunix.dll
0x00007fffbbbd0000 - 0x00007fffbbbe7000 	C:\WINDOWS\system32\napinsp.dll
0x00007fffbbba0000 - 0x00007fffbbbbb000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007fffbb8d0000 - 0x00007fffbb8ed000 	C:\WINDOWS\system32\wshbth.dll
0x00007fffcbf20000 - 0x00007fffcbf3d000 	C:\WINDOWS\system32\NLAapi.dll
0x00007fffcf4e0000 - 0x00007fffcf5aa000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007fffbbb40000 - 0x00007fffbbb52000 	C:\WINDOWS\System32\winrnr.dll
0x00007fffc7ee0000 - 0x00007fffc7eea000 	C:\Windows\System32\rasadhlp.dll
0x00007fffc6e60000 - 0x00007fffc6ee0000 	C:\WINDOWS\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Python313\Scripts;C:\Program Files\Python313;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
USERNAME=ntc
SHELL=C:\Program Files\Git\usr\bin\bash.exe
DISPLAY=needs-to-be-defined
LANG=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 0 days 16:29 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4028M free)
TotalPageFile size 22476M (AvailPageFile size 200M)
current process WorkingSet (physical memory assigned to process): 957M, peak: 958M
current process commit charge ("private bytes"): 1006M, peak: 1236M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
